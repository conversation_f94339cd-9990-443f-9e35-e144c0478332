import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/utils/wishlist_store_sharedprefrence.dart';
import 'package:share_plus/share_plus.dart';
import 'package:my_application/utils/custom_tab_indicator.dart';
import 'package:my_application/utils/string_constant.dart';
import 'package:my_application/widget/itemrandomproduct.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'Model/product.dart';
import 'Model/store.dart';
import 'galery_photo.dart';

class StoreDetail extends StatefulWidget {
  const StoreDetail({required this.idStore, super.key});

  final String idStore;

  @override
  State<StoreDetail> createState() => _StoreDetailState();
}

class _StoreDetailState extends State<StoreDetail> with SingleTickerProviderStateMixin {
  String store_name = "Boutique";
  late Future<Store> myFuturestore;
  late Future<List<Product>> myFuturelistProductsStore;
  late List<Product> ListProduct;
  bool more_item = true;
  bool loader_bottom = false;
  late TabController _controller;
  late String link_store ;


  String? dropdownNames;
  List<String> wishlist_store = [];
  late bool isInWishlistStore ;
  @override
  void initState() {
    super.initState();
    wishlist_store = WishlistStoreSharedprefrence().WishlistStoreSharedprefrence_data();
    isInWishlistStore = wishlist_store.contains(widget.idStore);
    link_store = "" ;
    _controller = TabController(
      length: 2,
      vsync: this,
    );
    ListProduct = [];
    myFuturestore = getdetailStore(widget.idStore);
    more_item = true;
    loader_bottom = false;
    getlistProductStore();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: DefaultTabController(
        length: 2,
        child: Scaffold(
            appBar: AppBar(
              surfaceTintColor: Colors.white,
              elevation: 3,
              shadowColor: Colors.black,
              backgroundColor: Colors.white,
              title: Text(store_name,style: TextStyle(fontSize: 16),),
              actions: [IconButton(onPressed: () {
                if(link_store != "") {
                  Share.share(link_store, subject: store_name);
                }
              }, icon: const Icon(Icons.share))],
            ),
            body: NestedScrollView(
              headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
                return [
                  SliverAppBar(
                    flexibleSpace: FlexibleSpaceBar(
                        background: FutureBuilder<Store>(
                            future: myFuturestore,
                            builder: (context, snapshot) {
                              if (!snapshot.hasData) {
                                return const Center(child: CircularProgressIndicator());
                              } else {
                                return headerStoreDetail(snapshot);
                              }
                            })),
                    surfaceTintColor: Colors.white,
                    backgroundColor: Colors.white,
                    automaticallyImplyLeading: false,
                    expandedHeight: 200,
                    floating: true,
                    snap: false,
                    pinned: false,
                    shadowColor: Colors.black,
                  ),
                  SliverAppBar(
                    pinned: true,
                    backgroundColor: Colors.white,
                    surfaceTintColor: Colors.white,
                    automaticallyImplyLeading: false,
                    title: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      mainAxisSize: MainAxisSize.max,
                      children: <Widget>[
                        Expanded(
                          flex: 7,
                          child: TabBar(
                              isScrollable: true,
                              tabAlignment: TabAlignment.start,
                              controller: _controller,
                              dividerColor: Colors.white,
                              indicator: const CustomTabIndicator(color: Colors.blueAccent, indicatorHeight: 2),
                              indicatorColor: Colors.blueAccent,
                              labelColor: Colors.blueAccent,
                              tabs: const [
                                Tab(text: "Boutique"),
                                Tab(text: "À propos")
                              ]),
                        ),
                        Expanded(
                          flex: 3,
                          child: GestureDetector(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Text(
                                  "Contacter",
                                  style: TextStyle(fontSize: 14),
                                ),
                                Icon(Icons.arrow_drop_down)
                              ],
                            ),
                            onTap: () {

                              myFuturestore.then((value) => {
                                if(value != null && value.telStore != ""){
                                  bottomsheetcontacter(context,value.telStore)
                                }
                              }
                              );
                            },
                          ),
                        )
                      ],
                    ),
                  ),
                ];
              },
              body: NotificationListener<ScrollEndNotification>(
                  onNotification: (scrollEnd) {
                    final metrics = scrollEnd.metrics;
                    if (metrics.atEdge) {
                      bool isTop = metrics.pixels == 0;
                      if (isTop) {
                      } else {
                        if (more_item == true) {
                          setState(() {
                            more_item = false;
                            getlistProductStore();
                            loader_bottom = true;
                          });
                        }
                      }
                    }
                    return true;
                  },
                  child: Stack(
                    children: [
                      TabBarView(
                        controller: _controller,
                        children: [
                          gridlistProduct(),
                          detailStore(),
                        ],
                      ),
                      loader_bottom == true
                          ? Positioned(
                              bottom: 0,
                              child: Container(
                                color: Colors.white,
                                height: 50,
                                width: MediaQuery.of(context).size.width,
                                child: const Center(
                                  child: Text(
                                    'Chargement...',
                                    style: TextStyle(fontSize: 18, fontFamily: 'Araboto'),
                                  ),
                                ),
                              ))
                          : SizedBox(),
                    ],
                  )),
            )),
      ),
    );
  }

  Future<Store> getdetailStore(String id_store) async {
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_info_store.php?id=$id_store'));
    if (res.statusCode == 200) {
      Store info_store =  Store.fromJson(json.decode(res.body)) ;
      link_store = info_store.urlStore! ;
      setState(() {
        store_name = info_store.nomStore! ;
      });
      return info_store;
    } else {
      return Store();
    }
  }

  Future<void> getlistProductStore() async {
    List<Product> newlistproduct = [];
    var res = await http.get(
        Uri.parse('${StringConstant.base_url}api/get_list_product_store.php?start=${ListProduct.length}&limit=10&orderby=1&id=${widget.idStore}'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      newlistproduct = List<Product>.from(l.map((model) => Product.fromJson(model)));
      setState(() {
        if (newlistproduct.length != 10) {
          more_item = false;
        } else {
          more_item = true;
        }
      });
      setState(() {
        ListProduct.addAll(newlistproduct);
        loader_bottom = false;
      });
    } else {}
  }

  Widget headerStoreDetail(snapshot) {
    return Stack(children: [
      GestureDetector(
        child: Container(
          height: 130,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: NetworkImage("${snapshot.data?.imageCouvertureStore}"),
              fit: BoxFit.cover,
              alignment: Alignment.center,
            ),
          ),
        ),
        onTap: (){
          List<String>  listphotocouvertur = ["${snapshot.data?.imageCouvertureStore}"] ;
          Navigator.push(context, MaterialPageRoute(builder: (context) => GaleryPhoto( position : 0,list_photo : listphotocouvertur )));
        },
      ),
      Align(
        alignment: Alignment.bottomCenter,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Padding(
              padding: const EdgeInsets.all(10.0),
              child: GestureDetector(
                child: Container(
                  height: 90,
                  width: 90,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(7)),
                    image: DecorationImage(
                      image: NetworkImage("${snapshot.data?.imageProfilStore}"),
                      fit: BoxFit.cover,
                      alignment: Alignment.center,
                    ),
                  ),
                ),
                onTap: (){
                  List<String>  listphotoprofil = ["${snapshot.data?.imageProfilStore}"] ;
                  Navigator.push(context, MaterialPageRoute(builder: (context) => GaleryPhoto( position : 0,list_photo : listphotoprofil )));
                },
              ),
            ),
            SizedBox(
              height: 70,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('${snapshot.data?.nomUtilStore}', style: const TextStyle(color: Colors.black), textAlign: TextAlign.start, maxLines: 1),
                    Row(
                      children: [
                        IntrinsicHeight(
                          child: Row(
                            children: [
                              Column(children: [
                                Text('${snapshot.data?.nbrArticleStore}',
                                    style: const TextStyle(color: Colors.black, fontSize: 10), textAlign: TextAlign.start, maxLines: 1),
                                const Text('Offres', style: TextStyle(color: Colors.black54, fontSize: 10), textAlign: TextAlign.start, maxLines: 1),
                              ]),
                              const VerticalDivider(
                                endIndent: 3,
                                indent: 3,
                                color: Colors.black54,
                                thickness: 0.5,
                              ),
                              Column(
                                children: [
                                  Text('${snapshot.data?.nbrAbonneStore}',
                                      style: const TextStyle(color: Colors.black, fontSize: 10), textAlign: TextAlign.start, maxLines: 1),
                                  const Text('Abonnés',
                                      style: TextStyle(color: Colors.black54, fontSize: 10), textAlign: TextAlign.start, maxLines: 1),
                                ],
                              ),
                            ],
                          ),
                        ),
                        abonnementButton(snapshot.data?.pack, snapshot.data?.idStore)
                      ],
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      )
    ]);
  }

  Widget abonnementButton(pack, idstore) {
    if (pack == "3") {
      if (isInWishlistStore) {
        // check if aready followed
        return GestureDetector(
          child: Container(
            margin: const EdgeInsets.only(left: 40),
            width: 80,
            child: Container(
              height: 20,
              width: 70,
              margin: const EdgeInsets.only(bottom: 5),
              decoration: const BoxDecoration(
                color: Colors.black26,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  const Text(
                    'Suivi',
                    style: TextStyle(color: Colors.black, fontSize: 10),
                  ),
                  Image.asset("assets/images/button_check_abonner.png", width: 16, height: 16)
                ],
              ),
            ),
          ),
          onTap: (){
            setState(() {
              isInWishlistStore = false ;
              WishlistStoreSharedprefrence().remove_from_WishlistStoreSharedprefrence_data(idstore);
            });
          },
        );
      } else {
        return GestureDetector(
          child: Container(
            margin: const EdgeInsets.only(left: 40),
            width: 80,
            child: Container(
              height: 20,
              width: 70,
              margin: const EdgeInsets.only(bottom: 5),
              decoration: const BoxDecoration(
                color: Colors.red,
              ),
              child: const Center(
                  child: Text(
                "Suivre",
                style: TextStyle(color: Colors.white, fontSize: 10),
              )),
            ),
          ),
          onTap: (){
            setState(() {
              isInWishlistStore = true ;
              WishlistStoreSharedprefrence().add_to_WishlistStoreSharedprefrence_data(idstore);
            });
          },
        );
      }
    } else {
      return const SizedBox();
    }
  }

  Widget gridlistProduct() {
    return GridView.builder(
        padding: const EdgeInsets.only(top: 20),
        gridDelegate:
            const SliverGridDelegateWithMaxCrossAxisExtent(mainAxisExtent: 280, maxCrossAxisExtent: 250, crossAxisSpacing: 0, mainAxisSpacing: 0),
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: ListProduct.length,
        itemBuilder: (BuildContext ctx, index) {
          return Itemrandomproduct(snapshot: ListProduct.elementAt(index), context: context, full: 2);
        });
  }

  Widget detailStore() {
    return FutureBuilder<Store>(
        future: myFuturestore,
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const Center(child: CircularProgressIndicator());
          } else {
            return Padding(
              padding: const EdgeInsets.all(10.0),
              child: Column(
                children: [
                  ListTile(
                    title: Text('${snapshot.data?.villeStore}'),
                    leading: Icon(Icons.my_location),
                    trailing: Text('ITINÉRAIRE'),
                    onTap: (){
                      if(Platform.isAndroid){
                        launchMapOnAndroid('${snapshot.data?.gpsStore}');
                      }
                      if(Platform.isIOS){
                        lauchMapOnIOS('${snapshot.data?.gpsStore}');
                      }
                    },
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        '${snapshot.data?.description}',
                        style: const TextStyle(
                          color: Colors.black45,
                        ),
                        textAlign: TextAlign.start,
                      ),
                    ],
                  ),
                  snapshot.data?.telStore != ""
                      ? ListTile(
                          title: Text('${snapshot.data?.telStore}', style: const TextStyle(fontSize: 14)),
                          leading: Image.asset("assets/images/call_outline.png", width: 20, height: 20),
                          onTap: () {
                            launchUrlString("tel://${snapshot.data?.telStore}");
                          },
                        )
                      : SizedBox(),
                  snapshot.data?.website != ""
                      ? ListTile(
                          title: Text('${snapshot.data?.website}', style: const TextStyle(fontSize: 11)),
                          leading: Image.asset("assets/images/browser_outline.png", width: 24, height: 24),
                          onTap: () {
                            _launchUrl('${snapshot.data?.website}');
                          })
                      : SizedBox(),
                  snapshot.data?.facebook != ""
                      ? ListTile(
                          title: Text('${snapshot.data?.facebook}', style: const TextStyle(fontSize: 11)),
                          leading: Image.asset("assets/images/facebook_outline.png", width: 24, height: 24),
                          onTap: () {
                            _launchUrl('${snapshot.data?.facebook}');
                          })
                      : SizedBox(),
                  snapshot.data?.instagram != ""
                      ? ListTile(
                          title: Text('${snapshot.data?.instagram}', style: const TextStyle(fontSize: 11)),
                          leading: Image.asset("assets/images/instagram_outline.png", width: 24, height: 24),
                          onTap: () {
                            _launchUrl('${snapshot.data?.instagram}');
                          })
                      : SizedBox(),
                  snapshot.data?.tiktok != ""
                      ? ListTile(
                          title: Text('${snapshot.data?.tiktok}', style: const TextStyle(fontSize: 11)),
                          leading: Image.asset("assets/images/tiktok_outline.png", width: 24, height: 24),
                          onTap: () {
                            _launchUrl('${snapshot.data?.tiktok}');
                          })
                      : SizedBox(),
                ],
              ),
            );
          }
        });
  }

  Future<void> _launchUrl(_url) async {
    final Uri url = Uri.parse(_url);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $_url');
    }
  }

  Future bottomsheetcontacter(context, String? telStore){

    return showModalBottomSheet(
        context: context,
        builder: (context) {
          return Padding(
            padding: const EdgeInsets.only(top:8.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                ListTile(
                  leading: Image.asset("assets/images/call.png"),
                  title: Text("$telStore"),
                  onTap: () {
                    launchUrlString("tel://${telStore}");
                  },
                ),
                SizedBox(height: 20,)
              ],
            ),
          );
        });


  }


  void launchMapOnAndroid(String uri_string) async {
    try {
      const String markerLabel = 'Here';
      final url = Uri.parse(uri_string);
      await launchUrl(url);
    } catch (error) {
    }
  }

  void lauchMapOnIOS(String uri_string) async {
    try {
      final url = Uri.parse(uri_string);
      if (await canLaunchUrl(url)) {
        await launchUrl(url);
      } else {
        throw 'Could not launch $url';
      }
    } catch (error) {
    }
  }

}
