import 'dart:convert';

import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/utils/string_constant.dart';

import '../Model/store.dart';

class WishlistStoreSharedprefrence extends GetxController{
  final GetStorage box = GetStorage();
  List<String> ListStore = [] ;
  WishlistStoreSharedprefrence();

  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();

  List<String> WishlistStoreSharedprefrence_data(){
    try {
    if(box.hasData('wishlist_store')) {
      ListStore = List<String>.from(box.read('wishlist_store'));
      if (ListStore.isNotEmpty) {
        return ListStore ;
      } else {
        return [];
      }
    }else{
      return [];
    }
    } catch (e) {
      // No specified type, handles all
      print('Something really unknown: $e');
    }

    return [] ;
  }

  void add_to_WishlistStoreSharedprefrence_data(String id_store){
    if(box.hasData('wishlist_store')) {
      ListStore = List<String>.from(box.read('wishlist_store'));
      ListStore.add(id_store);
      box.write('wishlist_store', ListStore);
    }else{
      ListStore.add(id_store);
      box.write('wishlist_store', ListStore);
    }
    call_add_to_wishlist_store_server(id_store);
  }

  void remove_from_WishlistStoreSharedprefrence_data(String id_store){
    if(box.hasData('wishlist_store')) {
      ListStore = List<String>.from(box.read('wishlist_store'));
      ListStore.remove(id_store);
      box.write('wishlist_store', ListStore);
    }else{
      ListStore.add(id_store);
      box.write('wishlist_store', ListStore);
    }
    call_remove_from_wishlist_store_server(id_store);
  }


  call_add_to_wishlist_store_server(id_store) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    await http.get(Uri.parse('${StringConstant.base_url}api/add_store_wishlist.php?token=${token}&id_store=${id_store}'));
  }

  call_remove_from_wishlist_store_server(id_store) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    await http.get(Uri.parse('${StringConstant.base_url}api/remove_store_wishlist.php?token=${token}&id_store=${id_store}'));
  }


  get_list_wishlist_store() async{
      List<String> list_id_store = [];
      box.write('wishlist_store' , list_id_store) ;
      String? token = await _prefs.then((data) => data.getString('user_token'));
      var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_store_wishlist.php?token=${token}'));
        Iterable l = json.decode(res.body);
        List<Store> liststore = List<Store>.from(l.map((model) => Store.fromJson(model)));
        liststore.forEach((store){
          list_id_store.add(store.idStore!);
        });
        box.write('wishlist_store', list_id_store);

  }

  void remove_all_WishlistStoreSharedprefrence_data(){
    box.erase();
  }

}