import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
class BuildCategorieItemShimmer extends StatefulWidget {
  const BuildCategorieItemShimmer({super.key});
  @override
  State<BuildCategorieItemShimmer> createState() => _BuildCategorieItemShimmerState();
}

class _BuildCategorieItemShimmerState extends State<BuildCategorieItemShimmer> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 70,
      child: Column(
        children: [
          Shimmer.fromColors(
            baseColor: Colors.grey.shade300,
            highlightColor: Colors.grey.shade100,
            child: Container(
              width: 50,
              height: 50,
              padding: EdgeInsets.all(5),
              margin: EdgeInsets.all(2),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: Colors.white,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(5),
            child: Center(
              child: Shimmer.fromColors(
                baseColor: Colors.grey.shade300,
                highlightColor: Colors.grey.shade100,
                child: Container(width: 50,height: 10,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: Colors.white,
                    )),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
