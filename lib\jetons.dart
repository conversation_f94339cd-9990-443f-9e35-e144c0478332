import 'dart:async';
import 'dart:convert';

import 'package:audioplayers/audioplayers.dart';
import 'package:countup/countup.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:lottie/lottie.dart';
import 'package:my_application/Model/boost_detail.dart';
import 'package:my_application/Model/gift_active.dart';
import 'package:my_application/Model/jeton_group.dart';
import 'package:my_application/Model/jeton_operations.dart';
import 'package:my_application/add_boost.dart';
import 'package:my_application/help_diamant.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:my_application/utils/string_constant.dart';
import 'package:my_application/widget/build_item_transaction.dart';
import 'package:my_application/widget/show_custom_loader_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

import 'Model/boost.dart';
import 'Model/response_gift_active_solde.dart';
import 'add_payment.dart';

class Jetons extends StatefulWidget {
  const Jetons({super.key});

  @override
  State<Jetons> createState() => _JetonsState();
}

class _JetonsState extends State<Jetons> {
  late List<JetonOperations> ListJetonOperations = [];
  late List<JetonGroup> ListJetonGroup = [];
  String nbr_jeton = '0' ;
  String old_nbr_jeton = '0' ;

  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();

  bool etat_gift = false ;
  String banner_gift = '' ;
  String solde_gift = '' ;
  late Timer _timer;
  bool _playAnimation = false;
  late AudioPlayer player = AudioPlayer();

  @override
  void initState() {
    super.initState();
    player = AudioPlayer();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await player.setSource(AssetSource('cheer.mp3'));
    });
    ListJetonOperations = [] ;
    ListJetonGroup = [] ;
    getlistBoost() ;
    get_gif_active();
  }

  @override
  void dispose() {
    _timer.cancel();
    player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: EdgeInsets.only(top: 16.0),
      child: Scaffold(
        backgroundColor: ColorConstant.background_home_page,
        appBar: AppBar(
          surfaceTintColor: Colors.white,
          elevation: 3,
          shadowColor: Colors.black,
          backgroundColor: Colors.white,
          title: const Text('Bi3 Asra3'),
            actions: [
              IconButton(
                icon: const Icon(Icons.help),
                onPressed: () {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => HelpDiamant()));
                },
              )
            ]
        ),
        body: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Stack(
            alignment: Alignment.center,
            children: [
              Column(
                children: [
                  Row(mainAxisSize: MainAxisSize.max,children: [Text('Mon Solde :',style: TextStyle(fontSize: 18))]),
                   Row(mainAxisSize: MainAxisSize.max,mainAxisAlignment: MainAxisAlignment.spaceBetween,children: [
                     _playAnimation ? Row(
                      children: [
                        Countup(
                          begin: double.parse(old_nbr_jeton),
                          end: double.parse(nbr_jeton),
                          duration: Duration(seconds: 5),
                          style:  TextStyle(color: Colors.black, fontSize: 28,fontWeight: FontWeight.bold),
                        ),
                        RichText(
                          text: TextSpan(
                              text: '',
                              style: TextStyle(
                                  color: Colors.black, fontSize: 28,fontWeight: FontWeight.bold),
                              children: <TextSpan>[
                                TextSpan(text: ' Diamants',
                                    style: TextStyle(
                                        color: Colors.black, fontSize: 14),
                                )
                              ]
                          ),
                        ),
                      ],
                    )
                    : RichText(
                      text: TextSpan(
                          text: '${nbr_jeton}',
                          style: TextStyle(
                              color: Colors.black, fontSize: 28,fontWeight: FontWeight.bold),
                          children: <TextSpan>[
                            TextSpan(text: ' Diamants',
                                style: TextStyle(
                                    color: Colors.black, fontSize: 14),
                            )
                          ]
                      ),
                    ),
                    InkWell(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 2, top: 4, right: 4, bottom: 2),
                          child: Container(
                              height: 40,
                              decoration: BoxDecoration(color: ColorConstant.red_enchere, borderRadius: BorderRadius.circular(7.0)),
                              child: const Padding(
                                padding: EdgeInsets.symmetric(horizontal: 8.0),
                                child: Center(
                                    child: Text("Achetez des Diamants", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal))),
                              )),
                        ),
                        onTap: () {
                          Show_list_groupe_token();
                        }),
                  ]),
                  const SizedBox(height: 20),

                  Row(mainAxisSize: MainAxisSize.max,mainAxisAlignment: MainAxisAlignment.center,children: [
                    Expanded(
                      flex: 10,
                      child: InkWell(
                          child: Container(
                              height: 40,
                              decoration: BoxDecoration(color: ColorConstant.red_enchere, borderRadius: BorderRadius.circular(7.0)),
                              child: const Padding(
                                padding: EdgeInsets.symmetric(horizontal: 8.0),
                                child: SizedBox(
                                  child: Center(
                                      child: Text("Bi3 Asra3", style: TextStyle(color: Colors.white, fontSize: 22, fontWeight: FontWeight.normal))),
                                ),
                              )),
                          onTap: () async{
                            var result = await Navigator.push(context, MaterialPageRoute(builder: (context) => AddBoost(id_offre : "")));
                            if(result == 'success') {
                              getlistBoost() ;
                              Fluttertoast.showToast(
                                  msg: "Votre Boost a été effectué avec succes",
                                  toastLength: Toast.LENGTH_LONG,
                                  gravity: ToastGravity.TOP,
                                  timeInSecForIosWeb: 5,
                                  backgroundColor: Colors.green,
                                  textColor: Colors.white,
                                  fontSize: 16.0);
                            }
                          }),
                    ),
                  ]),

                  SizedBox(height: 10),
                  etat_gift
                    ? InkWell(
                          onTap: (){
                            ShowCustomLoaderDialog().showCustomDialog(context);
                            gift_action_active();
                          },
                          child: Image.network('${banner_gift}',width: MediaQuery.of(context).copyWith().size.width * 0.90, height: MediaQuery.of(context).copyWith().size.width * 0.90))
                      : SizedBox(),

                  SizedBox(height: 10),
                  const Row(mainAxisSize: MainAxisSize.max,children: [Text('Transactions :',style: TextStyle(fontSize: 18))]),


                  ListJetonOperations.isEmpty
                      ? Center(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 15.0),
                            child: Text(
                              'Aucune transaction',
                              style: TextStyle(fontWeight: FontWeight.w100, color: Colors.black45),
                            ),
                          ),
                        )
                      : Expanded(
                        child: ListView.builder(
                            padding: EdgeInsets.only(bottom: 50, top: 10),
                            itemBuilder: (BuildContext context, int index) =>
                                ListJetonOperations.elementAt(index).typeOp == '1'
                                    ? InkWell(child: BuildItemTransaction(snapshot: ListJetonOperations.elementAt(index), context: context),onTap: () => Show_info_boost(ListJetonOperations.elementAt(index)))
                                    : BuildItemTransaction(snapshot: ListJetonOperations.elementAt(index), context: context),
                            itemCount: ListJetonOperations.length,
                          ),
                      ),
                ],
              ),
              _playAnimation
                  ? Positioned.fill(
                      child: Lottie.asset('assets/images/party.json', // Replace with your Lottie file path
                          repeat: true,
                          fit: BoxFit.fill,
                          width: MediaQuery.of(context).copyWith().size.width,
                          height: MediaQuery.of(context).copyWith().size.height),
                    )
                  : SizedBox(),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> getlistBoost() async {

    String? token = await _prefs.then((data) => data.getString('user_token'));
    //var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_info_boost.php?token=${token}'));


    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_info_boost.php?token=${token}'));
    if (res.statusCode == 200) {
      Boost _boost = Boost.fromJson(json.decode(res.body));
      setState(() {
        nbr_jeton = _boost.soldeToken!;
        old_nbr_jeton = _boost.soldeToken!;
        ListJetonOperations = _boost.jetonOperations! ;
        ListJetonGroup = _boost.jetonGroup! ;
      });
    }
  }

  Show_list_groupe_token(){
    double heigh_item = (ListJetonGroup.length * 60) + 80 ;
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height:40  ,child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Icon(Icons.horizontal_rule,size: 50,),
              ],
            )),
            const SizedBox(height:35  ,child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 8.0),
                  child: Text("Choisir le pack : ",style: TextStyle(fontSize: 22),),
                )
              ],
            )),
            SizedBox(
              height: heigh_item,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: ListView.builder(
                  physics: const NeverScrollableScrollPhysics() ,
                  padding: EdgeInsets.only(bottom: 50, top: 10),
                  itemBuilder: (BuildContext context, int index) =>
                      InkWell(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 2, top: 4, right: 4, bottom: 4),
                            child: Container(
                                height: 60,
                                decoration: BoxDecoration(color: ColorConstant.second_color, borderRadius: BorderRadius.circular(12.0)),
                                child: Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 8.0),
                                  child: Center(
                                      child: Text("${ListJetonGroup.elementAt(index).titre!} / ${ListJetonGroup.elementAt(index).qte!} Diamants  ", style: TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.normal))),
                                )),
                          ),
                          onTap: () async{
                            Navigator.pop(context);
                            final result = await Navigator.push(context, MaterialPageRoute(builder: (context) => AddPayment(id_offre :"${ListJetonGroup.elementAt(index).id!}")));

                            if(result == 'success') {
                              getlistBoost() ;
                              Fluttertoast.showToast(
                                  msg: "Votre achat a été effectué avec succes",
                                  toastLength: Toast.LENGTH_LONG,
                                  gravity: ToastGravity.TOP,
                                  timeInSecForIosWeb: 5,
                                  backgroundColor: Colors.green,
                                  textColor: Colors.white,
                                  fontSize: 16.0);
                            }
                            if(result == 'failure') {
                              getlistBoost() ;
                              Fluttertoast.showToast(
                                  msg: "Votre achat n'a pas été effectué , veuillez réessayer plus tard ",
                                  toastLength: Toast.LENGTH_LONG,
                                  gravity: ToastGravity.TOP,
                                  timeInSecForIosWeb: 5,
                                  backgroundColor: Colors.red,
                                  textColor: Colors.white,
                                  fontSize: 16.0);
                            }
                          }),
                  itemCount: ListJetonGroup.length,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
  Show_info_boost(JetonOperations jeton_operation) async {


    String? token = await _prefs.then((data) => data.getString('user_token'));


    var res = await http.get(Uri.parse('${StringConstant.base_url}api/info_boost.php?token=${token}&id=${jeton_operation.produitId}&boost_id=${jeton_operation.idBoostAnnonce}'));
    if (res.statusCode == 200) {
      BoostDetail _boost_detail = BoostDetail.fromJson(json.decode(res.body));
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return  Container(
            height: MediaQuery.of(context).copyWith().size.height * 0.80,
            child: Column(
              children: [
                SizedBox(height: 40, child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(Icons.horizontal_rule, size: 50,),
                  ],
                )),
                SizedBox(height: 35, child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text("Détails du boost : ", style: TextStyle(fontSize: 22),),
                    )
                  ],
                )),
                SizedBox(height: 35, child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Row(
                        children: [
                          Image.network('${_boost_detail.produitImg}'),
                          Text(" ${_boost_detail.produit}", style: TextStyle(fontSize: 18),),
                        ],
                      ),
                    )
                  ],
                )),
                SizedBox(height: 35, child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text("${_boost_detail.boost}", style: TextStyle(fontSize: 18),),
                    )
                  ],
                )),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text("Planification", style: TextStyle(fontSize: 16),),
                    )
                  ],
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: ListView.builder(
                      padding: EdgeInsets.only(bottom: 50, top: 10),
                      itemBuilder: (BuildContext context, int index) => Text('${_boost_detail.planification?.elementAt(index).dateplanifie}'),
                      itemCount: _boost_detail.planification?.length,
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      );
    }
  }

  get_gif_active() async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/gift_active.php?token=${token}'));
    if (res.statusCode == 200) {
      GiftActive _giftactive = GiftActive.fromJson(json.decode(res.body));
      if(_giftactive.etat == true ){
        setState(() {
          etat_gift = true ;
          banner_gift = _giftactive.img! ;
        });
      }else{
        setState(() {
          etat_gift = false ;
          banner_gift = '' ;
        });
      }
    }
  }
  gift_action_active() async{
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/gift_active_action.php?token=${token}'));
    if (res.statusCode == 200) {
      Navigator.of(context).pop();
      ResponseGiftActiveSolde _giftactive_solde = ResponseGiftActiveSolde.fromJson(json.decode(res.body));
      if(_giftactive_solde.solde != '' ) {
        await player.resume();
        old_nbr_jeton = nbr_jeton ;
        setState(() {
          etat_gift = false ;
          banner_gift = '' ;
          _playAnimation = true;
          nbr_jeton = _giftactive_solde.solde!;
        });
        _timer = Timer(Duration(seconds: 5), () {
          setState(() {
            _playAnimation = false;
             player.stop();
            getlistBoost() ;
          });
        });

      }else{
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text("Error"),backgroundColor: Colors.red,),
        );
      }
    }else{
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Error"),backgroundColor: Colors.red,),
      );
    }


  }

}
