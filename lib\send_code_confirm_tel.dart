import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:my_application/confirm_code_tel.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/utils/string_constant.dart';

import 'Model/response_inscription.dart';
import 'Model/response_request.dart';

class SendCodeConfirmTel extends StatefulWidget {
  SendCodeConfirmTel({super.key});

  @override
  State<SendCodeConfirmTel> createState() => _SendCodeConfirmTelState();
}

class _SendCodeConfirmTelState extends State<SendCodeConfirmTel> {
  late String _num_tel = "";
  bool _visibleerror = false;

  String errortext = "";

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                IconButton(onPressed: () => Navigator.of(context).pop(), icon: Icon(Icons.keyboard_backspace, color: ColorConstant.second_color))
              ]),
            ),
            const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              Image.asset(
                'assets/images/logo.png',
                height: 40,
              )
            ]),
            const SizedBox(height: 30),
            const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [Text("Bienvenue!", style: TextStyle(color: Colors.black, fontSize: 14, decoration: TextDecoration.none))]),
            const SizedBox(height: 5),
            Row(mainAxisAlignment: MainAxisAlignment.center, mainAxisSize: MainAxisSize.max, children: [
              SizedBox(
                width: MediaQuery.of(context).size.width,
                child: const Padding(
                    padding: EdgeInsets.only(right: 30, left: 30),
                    child: Text("Remplissez le formulaire pour commencer",
                        style: TextStyle(color: Colors.black45, fontSize: 12, decoration: TextDecoration.none),
                        softWrap: true,
                        textAlign: TextAlign.center)),
              )
            ]),
            const SizedBox(height: 10),
            SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Padding(
                  padding: const EdgeInsets.only(right: 30, left: 30),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                     mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: ColorConstant.gray_background_detail_product,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(right: 8.0, left: 8.0,top: 5.0,bottom: 5.0),
                          child: const Text("Confirmez votre numéro de téléphone",
                              style: TextStyle(color: Colors.black45, fontSize: 12, decoration: TextDecoration.none),
                              softWrap: true,
                              textAlign: TextAlign.center),
                        ),
                      ),
                    ],
                  )),
            ),
            const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 30, left: 30),
                    child: TextFormField(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        fillColor: Colors.white,
                        labelText: "Numéro de téléphone",
                        labelStyle: TextStyle(fontSize: 14, color: Colors.black),
                        prefixIcon: Padding(
                          padding: EdgeInsets.only(right: 8.0, left: 8.0),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.phone,
                                color: Colors.black,
                              ),
                              Text("+216")
                            ],
                          ),
                        ),
                        filled: true,
                      ),
                      autofocus: false,
                      cursorColor: Colors.black,
                      keyboardType: TextInputType.number,
                      textInputAction: TextInputAction.done,
                      onChanged: (value) => _num_tel = value,
                    ),
                  ))
            ]),
            const SizedBox(height: 10),
            _visibleerror == true
                ? SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 30.0, top: 10.0, bottom: 10.0),
                      child: Text(errortext,
                          style: const TextStyle(color: Colors.red, fontSize: 12, decoration: TextDecoration.none),
                          softWrap: true,
                          textAlign: TextAlign.start),
                    ),
                  )
                : const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                height: 50,
                width: MediaQuery.of(context).size.width,
                child: Padding(
                  padding: const EdgeInsets.only(right: 30, left: 30),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorConstant.red_enchere,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    onPressed: () {
                      send_code_confirm() ;
                    },
                    child: const Center(child: Text("Envoyer", style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 13))),
                  ),
                ),
              )
            ]),
            const SizedBox(height: 20),
          ]),
        ),
      ),
    );
  }

  void send_code_confirm() async {
    try {
      var res = await http.post(Uri.parse('${StringConstant.base_url}templates/verif_inscri.php'), headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      }, body: {
        'phone': _num_tel,
        'code': "",
        'pass': "",
        'cgu_confirm': "",
        'email': "",
        'villeInscrit': "",
        'nom': "",
        'step': "phone",
        'token_fcm': "",
      });
      if (res.statusCode == 200) {
        ResponseInscription responseinscription = ResponseInscription.fromJson(jsonDecode(res.body));
        if (responseinscription != null) {
          if (responseinscription.error == "1") {
            setState(() {
              errortext = responseinscription.message!;
              _visibleerror = true;
            });
          } else {
              ///////////////////////////////////////
              //// go to confirm_code_tel////////////
              ///////////////////////////////////////

              Navigator.push(context, MaterialPageRoute(builder: (context) => ConfirmCodeTel(_num_tel)));

          }
        } else {
          setState(() {
            errortext = "Une erreur s'est produite, contactez le support";
            _visibleerror = true;
          });
        }
      } else {
        setState(() {
          errortext = "Une erreur s'est produite, contactez le support";
          _visibleerror = true;
        });
      }
    } on Exception catch (e) {
      print(e);
      throw Exception("Error on server");
    }
  }
}
