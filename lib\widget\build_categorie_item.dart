import 'package:flutter/material.dart';

import '../Model/Categorie.dart';
import '../product_list.dart';
import '../utils/string_constant.dart';
class BuildCategorieItem extends StatefulWidget{

  Categorie elementAt ;
  BuildContext context ;

  BuildCategorieItem(this.elementAt, this.context, {super.key});

  @override
  State<BuildCategorieItem> createState() => _BuildCategorieItemState();
}

class _BuildCategorieItemState extends State<BuildCategorieItem> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        width: 70,
        child: Column(
          children: [
            Container(
                width: 50,
                height: 50,
                padding: const EdgeInsets.all(5),
                margin: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  color: Colors.white,
                ),
                child: Image.network(
                  widget.elementAt.icon!,
                  errorBuilder: (context, error, stackTrace) {
                    return Image.network("${StringConstant.base_url}api/5173005-128.png");
                  },
                )),
            Padding(
              padding: const EdgeInsets.all(5),
              child: Center(
                child: Text(
                  textAlign: TextAlign.center,
                  widget.elementAt.nomCategorieProduit!,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(color: Colors.black, fontSize: 9),
                ),
              ),
            ),
          ],
        ),
      ),
      onTap: () {
        Navigator.push(context, MaterialPageRoute(builder: (context) => ProductList("", "${widget.elementAt.id}", order: "1")));
      },
    );
  }
}