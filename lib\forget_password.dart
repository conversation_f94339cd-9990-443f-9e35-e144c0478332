import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/utils/string_constant.dart';
import 'Model/response_request.dart';

class ForgetPassword extends StatefulWidget {
  ForgetPassword({super.key});

  @override
  State<ForgetPassword> createState() => _ForgetPasswordState();
}

class _ForgetPasswordState extends State<ForgetPassword> {

  late String _email = "";
  bool _visibleerror = false;
  Color color_message = Colors.black ;
  String errortext = "";

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                IconButton(onPressed: () => Navigator.of(context).pop(), icon: Icon(Icons.keyboard_backspace, color: ColorConstant.second_color))
              ]),
            ),
            const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              Image.asset(
                'assets/images/logo.png',
                height: 40,
              )
            ]),
            const SizedBox(height: 30),
            const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [Text("Mot de passe oublié", style: TextStyle(color: Colors.black, fontSize: 14, decoration: TextDecoration.none))]),
            const SizedBox(height: 5),
            Row(mainAxisAlignment: MainAxisAlignment.center
                ,mainAxisSize: MainAxisSize.max, children: [
              SizedBox(
                width: MediaQuery.of(context).size.width,
                child: const Padding(
                  padding: EdgeInsets.only(right: 30, left: 30),
                  child: Text("Entre ton adresse e-mail pour réinitialiser votre mot de passe", style: TextStyle(color: Colors.black45, fontSize: 12, decoration: TextDecoration.none),softWrap: true,textAlign: TextAlign.center)
                ),
              )
            ]),
            const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 30, left: 30),
                    child: TextFormField(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        fillColor: Colors.white,
                        labelText: "Adresse e-mail",
                        labelStyle: TextStyle(fontSize: 14, color: Colors.black),
                        prefixIcon: Icon(
                          Icons.email_outlined,
                          color: Colors.black,
                        ),
                        filled: true,
                      ),
                      autofocus: false,
                      cursorColor: Colors.black,
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.done,
                      onChanged: (value) => _email = value,
                    ),
                  ))
            ]),
            const SizedBox(height: 10),
            _visibleerror == true
                ? SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 30.0, top: 10.0, bottom: 10.0),
                      child: Text(
                        errortext,
                        style: TextStyle(color: color_message, fontSize: 12, decoration: TextDecoration.none),
                        softWrap: true,
                        textAlign: TextAlign.start
                      ),
                    ),
                )
                : const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                height: 50,
                width: MediaQuery.of(context).size.width,
                child: Padding(
                  padding: const EdgeInsets.only(right: 30, left: 30),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorConstant.red_enchere,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    onPressed: () {
                      recovery_password();
                    },
                    child: const Center(child: Text("Confirmer", style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 13))),
                  ),
                ),
              )
            ]),
            const SizedBox(height: 20),
          ]),
        ),
      ),
    );
  }

  void recovery_password() async {
    try {
      var res = await http.post(Uri.parse('${StringConstant.base_url}api/recovery_password.php'), headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      }, body: {
        'email': _email,
      });
      if (res.statusCode == 200) {
        ResponseRequest responserequest = ResponseRequest.fromJson(jsonDecode(res.body));
        if(responserequest != null ) {
          setState(() {
            errortext = responserequest.text!;
            _visibleerror = true;
          });
          if (responserequest.etat == "0") {
            setState(() {
              color_message = Colors.black ;
            });
          } else {
            setState(() {
              color_message = Colors.red ;
            });
          }
        } else {
          setState(() {
            errortext = "Une erreur s'est produite, contactez le support";
            _visibleerror = true;
            color_message = Colors.red ;
          });
        }
      } else {
        setState(() {
          errortext = "Une erreur s'est produite, contactez le support";
          _visibleerror = true;
          color_message = Colors.red ;
        });
      }
    } on Exception catch (e) {
      print(e);
      throw Exception("Error on server");
    }
  }
}
