import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/utils/string_constant.dart';
import 'Model/response_inscription.dart';
import 'dart:math' as math;

import 'Model/response_login.dart';
import 'Model/ville.dart';
import 'main.dart';

class Inscription extends StatefulWidget {
  String user_tel = "";
  String tel_code = "";

  Inscription(this.user_tel, this.tel_code, {super.key});

  @override
  State<Inscription> createState() => _InscriptionState();
}

class _InscriptionState extends State<Inscription> {
  bool _visibleerror = false;
  bool checked_condition_general = false;

  String errortext = "";
  String _confirm_password = "";
  String _password = "";
  String _ville = "";
  String _user_name = "";
  String _email = "";

  late Ville dropdownvalue;

  List<Ville> items_ville = [];

  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
  String fcm_token = "" ;
  @override
  void initState() {
    items_ville = Ville.get_all_ville_parent();
    dropdownvalue = items_ville.first;

    try {
      FirebaseMessaging.instance.getToken().then((_fcm_token){
        fcm_token = _fcm_token!;
      });
    } catch (e) {
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                IconButton(onPressed: () => Navigator.of(context).pop(), icon: Icon(Icons.keyboard_backspace, color: ColorConstant.second_color))
              ]),
            ),
            const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              Image.asset(
                'assets/images/logo.png',
                height: 40,
              )
            ]),
            const SizedBox(height: 30),
            const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [Text("Bienvenue!", style: TextStyle(color: Colors.black, fontSize: 14, decoration: TextDecoration.none))]),
            const SizedBox(height: 5),
            Row(mainAxisAlignment: MainAxisAlignment.center, mainAxisSize: MainAxisSize.max, children: [
              SizedBox(
                width: MediaQuery.of(context).size.width,
                child: const Padding(
                    padding: EdgeInsets.only(right: 30, left: 30),
                    child: Text("Remplissez le formulaire pour commencer",
                        style: TextStyle(color: Colors.black45, fontSize: 12, decoration: TextDecoration.none),
                        softWrap: true,
                        textAlign: TextAlign.center)),
              )
            ]),
            const SizedBox(height: 20),
            // nom et prenom
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 30, left: 30),
                    child: TextFormField(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        fillColor: Colors.white,
                        labelText: "Nom et prénom",
                        labelStyle: TextStyle(fontSize: 14, color: Colors.black),
                        prefixIcon: Padding(
                          padding: EdgeInsets.only(right: 8.0, left: 8.0),
                          child: Icon(
                            Icons.person_rounded,
                            color: Colors.black,
                          ),
                        ),
                        filled: true,
                      ),
                      autofocus: false,
                      cursorColor: Colors.black,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.done,
                      onChanged: (value) => _user_name = value,
                    ),
                  ))
            ]),
            const SizedBox(height: 10),
            // email
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 30, left: 30),
                    child: TextFormField(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        fillColor: Colors.white,
                        labelText: "Email",
                        labelStyle: TextStyle(fontSize: 14, color: Colors.black),
                        prefixIcon: Padding(
                          padding: EdgeInsets.only(right: 8.0, left: 8.0),
                          child: Icon(
                            Icons.mail_outline_rounded,
                            color: Colors.black,
                          ),
                        ),
                        filled: true,
                      ),
                      autofocus: false,
                      cursorColor: Colors.black,
                      keyboardType: TextInputType.emailAddress,
                      textInputAction: TextInputAction.done,
                      onChanged: (value) => _email = value,
                    ),
                  ))
            ]),
            const SizedBox(height: 10),
            // ville
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Padding(
                      padding: const EdgeInsets.only(right: 30, left: 30),
                      child: DecoratedBox(
                          decoration: BoxDecoration(
                            border: Border.all(width: 1, color: Colors.black),
                            borderRadius: BorderRadius.circular(5),
                          ),
                          child: Row(
                            children: [
                              const Flexible(
                                  flex: 1,
                                  child: Padding(
                                    padding: EdgeInsets.only(left: 8.0, right: 8.0),
                                    child: Icon(Icons.location_pin, color: Colors.black),
                                  )),
                              Flexible(
                                flex: 9,
                                child: Padding(
                                  padding: const EdgeInsets.only(left: 10, right: 10),
                                  child: DropdownButton<Ville>(
                                    hint: const Text(
                                      "Ville",
                                      style: TextStyle(color: Colors.black),
                                    ),
                                    value: dropdownvalue,
                                    icon: const Icon(Icons.arrow_drop_down),
                                    items: items_ville.map((Ville items) {
                                      return DropdownMenuItem<Ville>(
                                        value: items,
                                        child: Text(items.nom!),
                                      );
                                    }).toList(),
                                    onChanged: (Ville? newValue) {
                                      setState(() {
                                        dropdownvalue = newValue!;
                                      });
                                    },
                                    isExpanded: true,
                                    underline: Container(),
                                    dropdownColor: Colors.white,
                                    iconEnabledColor: Colors.black,
                                  ),
                                ),
                              ),
                            ],
                          ))))
            ]),
            const SizedBox(height: 10),
            // mot de passe
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 30, left: 30),
                    child: TextFormField(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        fillColor: Colors.white,
                        labelText: "Mot de passe",
                        labelStyle: TextStyle(fontSize: 14, color: Colors.black),
                        prefixIcon: Padding(
                          padding: EdgeInsets.only(right: 8.0, left: 8.0),
                          child: Icon(
                            Icons.lock_outline,
                            color: Colors.black,
                          ),
                        ),
                        filled: true,
                      ),
                      autofocus: false,
                      cursorColor: Colors.black,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.done,
                      onChanged: (value) => _password = value,
                    ),
                  ))
            ]),
            const SizedBox(height: 10),
            // confirm mot de passe
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Padding(
                    padding: const EdgeInsets.only(right: 30, left: 30),
                    child: TextFormField(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        fillColor: Colors.white,
                        labelText: "Confirmation du mot de passe",
                        labelStyle: TextStyle(fontSize: 14, color: Colors.black),
                        prefixIcon: Padding(
                          padding: EdgeInsets.only(right: 8.0, left: 8.0),
                          child: Icon(
                            Icons.more_horiz_rounded,
                            color: Colors.black,
                          ),
                        ),
                        filled: true,
                      ),
                      autofocus: false,
                      cursorColor: Colors.black,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.done,
                      onChanged: (value) => _confirm_password = value,
                    ),
                  ))
            ]),

            const SizedBox(height: 10),
            SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Padding(
                padding: const EdgeInsets.only(right: 30, left: 30),
                child: Row(
                  children: [
                    Checkbox(
                      checkColor: Colors.white,
                      activeColor: ColorConstant.second_color,
                      value: checked_condition_general,
                      onChanged: (bool? value) {
                        setState(() {
                          checked_condition_general = value!;
                        });
                      },
                    ),
                    GestureDetector(
                      child: Text("Conditions générales",
                          style: TextStyle(
                              color: ColorConstant.second_color, decoration: TextDecoration.underline, decorationColor: ColorConstant.second_color)),
                      onTap: () {
                        openWebViewDialogConditionGenaral(context, "${StringConstant.base_url}templates/condition_general.php");
                      },
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 10),
            _visibleerror == true
                ? SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 30.0, right: 30.0, top: 10.0, bottom: 10.0),
                      child: Text(errortext,
                          style: const TextStyle(color: Colors.red, fontSize: 12, decoration: TextDecoration.none),
                          softWrap: true,
                          textAlign: TextAlign.start),
                    ),
                  )
                : const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                height: 50,
                width: MediaQuery.of(context).size.width,
                child: Padding(
                  padding: const EdgeInsets.only(right: 30, left: 30),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorConstant.red_enchere,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    onPressed: () {
                      ///////// confirm_code() ;

                      if ((_user_name != "") &&
                          (_email != "") &&
                          (_password != "") &&
                          (_confirm_password != "") &&
                          (_password == _confirm_password) &&
                          (dropdownvalue.nom != "ville")) {
                            if (checked_condition_general == true) {
                              send_inscription() ;
                            } else {
                              setState(() {
                                errortext = "Veuillez accepter les conditions générales";
                                _visibleerror = true;
                              });
                            }
                      } else {
                        setState(() {
                          errortext = "Veuillez vérifier votre saisie";
                          _visibleerror = true;
                        });
                      }
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Center(child: Text("Terminer mon inscription", style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 13))),
                        const SizedBox(width: 30),
                        Transform(transform: Matrix4.rotationY(math.pi), child: const Icon(Icons.gavel, color: Colors.white)),
                      ],
                    ),
                  ),
                ),
              )
            ]),
            const SizedBox(height: 20),
          ]),
        ),
      ),
    );
  }

  void send_inscription() async {
    print("'phone': ${widget.user_tel} code: ${widget.tel_code} =pass= $_password cgu_confirm': '1' email': $_email villeInscrit': $_ville nom': $_user_name ");
    try {
      var res = await http.post(Uri.parse('${StringConstant.base_url}templates/verif_inscri.php'), headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      }, body: {
        'phone': widget.user_tel,
        'code': widget.tel_code,
        'pass': _password,
        'cgu_confirm': "1",
        'email': _email,
        'villeInscrit': dropdownvalue.nom,
        'nom': _user_name,
        'step': "pass",
        'token_fcm': "$fcm_token",
      });

      if (res.statusCode == 200) {
        ResponseInscription responseinscription = ResponseInscription.fromJson(jsonDecode(res.body));
        if (responseinscription != null) {
          if (responseinscription.error == "1") {
            setState(() {
              errortext = responseinscription.message!;
              _visibleerror = true;
            });
          } else {
            /////////////////////////////////////////////////////////
            //// execute authentification  login() //////////////////
            /////////////////////////////////////////////////////////
            login();
          }
        } else {
          setState(() {
            errortext = "Une erreur s'est produite, contactez le support";
            _visibleerror = true;
          });
        }
      } else {
        setState(() {
          errortext = "Une erreur s'est produite, contactez le support";
          _visibleerror = true;
        });
      }
    } on Exception catch (e) {
      print(e);
      throw Exception("Error on server");
    }
  }

  void openWebViewDialogConditionGenaral(BuildContext context, String url) async {
    var res = await http.post(Uri.parse('${StringConstant.base_url}templates/condition_general.php'));
    if (res.statusCode == 200) {
      // ignore: use_build_context_synchronously
       showDialog(
        context: context,
        builder: (BuildContext context) {
          return  AlertDialog(
            backgroundColor: Colors.white,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
            surfaceTintColor: Colors.white,
            title: const Text('Conditions générales'),
            content: SizedBox(
                width: MediaQuery.of(context).size.width - 100,
                height: MediaQuery.of(context).size.height - 200,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Html(data: res.body),
                    ],
                  ),
                )),
            actions: <Widget>[
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text('Fermer'),
              ),
            ],
          );
        },
      );
    }
  }


  void login() async {
    try {

      var res = await http.post(Uri.parse('${StringConstant.base_url}api/login.php'),
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: {
            'user_tel': widget.user_tel,
            'user_password': _password,
            'token_fcm': "",
          });
      if (res.statusCode == 200) {
        ResponseLogin responselogin = ResponseLogin.fromJson(jsonDecode(res.body));
        if (responselogin.etat == "0") {
          _prefs.then((data) => {
            data.setString('user_tel', widget.user_tel),
            data.setString('user_password', _password),
            data.setString('id_user', "${responselogin.iduser}"),
            data.setString('user_token', "${responselogin.token}")
          });
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(builder: (context) => MyHomePage()),
                (route) => false, // This removes all the previous routes
          );
        } else {
          setState(() {
            errortext = "Vérifier votre numéro de téléphone et mot de passe";
            _visibleerror = true;
          });
        }
      } else {
        errortext = "Une erreur s'est produite, contactez le support";
        _visibleerror = true;
      }
    } on Exception catch (e) {
      print(e);
      throw Exception("Error on server");
    }
  }
}
