import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import '../Model/achat.dart';
import '../product_detail.dart';

class BuildItemAchat extends StatefulWidget {
  final BuildContext context;
  final Achat snapshot;

  BuildItemAchat({super.key, required this.snapshot, required this.context});

  @override
  State<BuildItemAchat> createState() => _BuildItemAchatState();
}

class _BuildItemAchatState extends State<BuildItemAchat> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Card(
        margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "${widget.snapshot.titreProduit}",
                      style: const TextStyle(fontSize: 18),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.start,
                    )
                  ]),
              Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text("Date publication",
                        style: TextStyle(fontSize: 12)),
                    Expanded(
                        child: Html(data: "${widget.snapshot.date}", style: {
                      "body": Style(
                          fontSize: FontSize(12.0), textAlign: TextAlign.end)
                    }))
                  ]),
              Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text("Prix", style: TextStyle(fontSize: 12)),
                    Text("${widget.snapshot.prix} DT")
                  ]),
            ],
          ),
        ),
      ),
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => ProductDetail(
                      idProduct: widget.snapshot.id,
                      nomProduct: widget.snapshot.titreProduit,
                    )));
      },
    );
  }
}
