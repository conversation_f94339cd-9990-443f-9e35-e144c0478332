import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:shimmer/main.dart';

import '../Model/product.dart';
import '../Model/ville.dart';
import '../add_boost.dart';
import '../login.dart';
import '../product_detail.dart';
import '../utils/check_authenticate.dart';
import '../utils/color_constant.dart';
import '../utils/string_constant.dart';
import '../utils/wishlist_product_sharedprefrence.dart';

class ItemHomeMesAnnonce extends StatefulWidget {
  final int full;
  final BuildContext context;
  final Product snapshot;

  ItemHomeMesAnnonce({super.key, required this.snapshot, required this.context, required this.full});

  @override
  State<ItemHomeMesAnnonce> createState() => _ItemHomeMesAnnonceState();
}

class _ItemHomeMesAnnonceState extends State<ItemHomeMesAnnonce> {

  CkeckAuthenticate ckeckAuthenticate = Get.put(CkeckAuthenticate());

  Timer? countdownTimer;
  late Duration myDuration = DateTime.parse(widget.snapshot.finDateProduit!).difference(DateTime.now()); //Duration(seconds: 10);

  late bool islogged = false;
  bool liked = false;

  @override
  void initState() {
    ckeckAuthenticate.checkAuthenticate_user().then((value) {
      islogged = value;
    });
    liked = WishlistProductSharedprefrence().check_exist_in_whishlist(widget.snapshot?.id);
    if (widget.snapshot.directProduit == "0") {
      DateTime now = new DateTime.now();
      myDuration = DateTime.parse(widget.snapshot.finDateProduit!).difference(now);
      //put date for test
      /* var dt1 = DateTime.parse('2023-08-02 14:58:00');
           myDuration = dt1.difference(now);*/
      startTimer();
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double sizewidth = MediaQuery.of(context).size.width;
    double item_size;
    if (widget.full == 1) {
      item_size = sizewidth;
    } else {
      item_size = sizewidth / 2;
    }
    return GestureDetector(
      child: Container(
        width: item_size,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(topLeft: Radius.circular(7), topRight: Radius.circular(7)),
        ),
        margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
        child: Card(
          margin: const EdgeInsets.all(0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
          color: Colors.white,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Expanded(
                flex: 7,
                child: Stack(
                  alignment: Alignment.topCenter,
                  //clipBehavior: Clip.none,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.only(topLeft: Radius.circular(7), topRight: Radius.circular(7)),
                        image: DecorationImage(
                          image:NetworkImage("${StringConstant.base_url_image_product}${widget.snapshot?.idUtilisateur}/${widget.snapshot?.linkImage}"),
                          fit: BoxFit.cover,
                          alignment: Alignment.center,
                        ),
                      ),
                    ),
                    Positioned(
                      top: 5,
                      right: 5,
                      child: Container(
                        width: 25,
                        height: 25,
                        margin: EdgeInsets.all(5),
                        decoration: BoxDecoration(color: Colors.white, shape: BoxShape.circle),
                        child: Center(
                          child: Obx(
                                () => ckeckAuthenticate.loggedin == true.obs
                              ? liked
                                  ? GestureDetector(
                                      onTap: () {
                                        setState(() {;
                                          WishlistProductSharedprefrence().remove_from_WishlistProductSharedprefrence_data("${widget.snapshot.id}");
                                          liked = false;
                                        });
                                      },
                                      child: Icon(Icons.favorite, color: ColorConstant.second_color, size: 20))
                                  : GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          WishlistProductSharedprefrence().add_to_WishlistProductSharedprefrence_data("${widget.snapshot.id}");
                                          liked = true;
                                        });
                                      },
                                      child: Icon(Icons.favorite_border_outlined, color: ColorConstant.gary_marker, size: 20))
                              : GestureDetector(
                                  onTap: () {

                                    Navigator.push(context, MaterialPageRoute(builder: (context) => Login())).whenComplete(() {
                                      ckeckAuthenticate.checkAuthenticate_user().then((value) {
                                        setState(() {
                                          islogged = value;
                                          if (islogged == true) {
                                            liked = WishlistProductSharedprefrence().check_exist_in_whishlist(widget.snapshot?.id);
                                          }
                                        });
                                      });
                                    });
                                  },
                                  child: Icon(Icons.favorite_border_outlined, color: ColorConstant.gary_marker, size: 20))),
                        ),
                      ),
                    ),
                    widget.snapshot.directProduit == "0"
                        ? Positioned(
                            // achat enchere
                            bottom: 0,
                            right: 0,
                            left: 0,
                            height: 40,
                            child: type_encheredisplay(widget.snapshot))
                        : Positioned(
                            // achat direct
                            bottom: 0,
                            right: 0,
                            height: 50,
                            child: type_negociableedisplay())
                  ],
                ),
              ),
              Expanded(
                flex: 3,
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0 ,right: 8.0),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              "${widget.snapshot?.titreProduit}",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.start,
                              style: const TextStyle(color: Colors.black, fontSize: 14),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0 ,right: 8.0),
                      child: Row(
                        children: [
                          Expanded(child: return_prix_bottom_card(widget.snapshot)),
                        ],
                      ),
                    ),
                    Row(mainAxisSize: MainAxisSize.max, mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                      Expanded(
                        flex: 10,
                        child: Padding(
                          padding: const EdgeInsets.only(top :2.0),
                          child: InkWell(
                              child: Container(
                                  height: 30,
                                  decoration: BoxDecoration(color: ColorConstant.red_enchere, borderRadius: BorderRadius.only(bottomRight: Radius.circular(7.0),bottomLeft:  Radius.circular(7.0))),
                                  child: const Padding(
                                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                                    child: SizedBox(
                                      child: Center(
                                          child: Text("Bi3 Asra3", style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.normal))),
                                    ),
                                  )),
                              onTap: () async{
                                var result = await Navigator.push(context, MaterialPageRoute(builder: (context) => AddBoost(id_offre: '${widget.snapshot.id}')));
                                if(result == 'success') {
                                  Fluttertoast.showToast(
                                      msg: "Votre Boost a été effectué avec succes",
                                      toastLength: Toast.LENGTH_LONG,
                                      gravity: ToastGravity.TOP,
                                      timeInSecForIosWeb: 5,
                                      backgroundColor: Colors.green,
                                      textColor: Colors.white,
                                      fontSize: 16.0);
                                }
                              }),
                        ),
                      ),
                    ]),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => ProductDetail(
                      idProduct: widget.snapshot.id,
                      nomProduct: widget.snapshot.titreProduit,
                    )));
      },
    );
  }

  /*@override
  void dispose() {
    super.dispose();
    stopTimer();
  }*/

  Widget type_negociableedisplay() {
    if (widget.snapshot.propositionPrixProduit == "0") {
      return SizedBox();
    } else {
      return SizedBox(
        height: 25,
        child: Container(
          margin: const EdgeInsets.only(top: 20, bottom: 5),
          height: 25,
          decoration: BoxDecoration(
            color: ColorConstant.green_enchere,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(40),
              bottomLeft: Radius.circular(40),
            ),
          ),
          child: const Center(
            child: Padding(
                padding: EdgeInsets.only(left: 10, right: 10), child: Text("Négociable", style: TextStyle(color: Colors.white, fontSize: 12))),
          ),
        ),
      );
    }
  }

  Widget type_encheredisplay(Product snapshot) {
    String timer_enchere = "";

    String strDigits(int n) => n.toString().padLeft(2, '0');
    final days = strDigits(myDuration.inDays);
    final hours = strDigits(myDuration.inHours.remainder(24));
    final minutes = strDigits(myDuration.inMinutes.remainder(60));
    final seconds = strDigits(myDuration.inSeconds.remainder(60));

    if (days == "00") {
      if (hours == "00") {
        if (minutes == "00") {
          if (seconds == "00") {
            timer_enchere = "Offre Terminée";
          } else {
            timer_enchere = "Reste ${int.parse(seconds)} sec";
          }
        } else {
          timer_enchere = "Reste ${int.parse(minutes)}min ${int.parse(seconds)}sec ";
        }
      } else {
        if (minutes == "00") {
          timer_enchere = "Reste ${int.parse(hours)}h";
        } else {
          timer_enchere = "Reste ${int.parse(hours)}h ${int.parse(minutes)}min ";
        }
      }
    } else {
      if (hours == "00") {
        timer_enchere = "Reste ${int.parse(days)}j";
      } else {
        timer_enchere = "Reste ${int.parse(days)}j ${int.parse(hours)}h ";
      }
    }

    if (int.parse(seconds) < 0) {
      timer_enchere = "Offre Terminée";
    }

    return Container(
      decoration: BoxDecoration(
        color: ColorConstant.dart_transparent,
      ),
      height: 20,
      child: Stack(
        children: [
          Positioned(
            left: 0,
            bottom: 0,
            child: Padding(
              padding: EdgeInsets.only(left: 8.0, bottom: 5),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  RichText(
                    text: TextSpan(
                      text: "Départ : ",
                      style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                      children: <TextSpan>[
                        snapshot.nbrEnchereProduit == "0"
                            ? TextSpan(
                                text: "${snapshot.prixProduit}",
                                style: const TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.bold))
                            : TextSpan(
                                text: "${snapshot.dernierEnchereProduit}",
                                style: const TextStyle(color: Colors.white, fontSize: 14, fontWeight: FontWeight.bold)),
                        const TextSpan(text: " DT", style: TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold)),
                      ],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text("$timer_enchere", style: TextStyle(color: Colors.white, fontSize: 10)),
                ],
              ),
            ),
          ),
          Positioned(
            right: 0,
            bottom: 0,
            child: Container(
              margin: const EdgeInsets.only(top: 5, bottom: 5),
              height: 25,
              decoration: BoxDecoration(
                color: ColorConstant.red_enchere,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(40),
                  bottomLeft: Radius.circular(40),
                ),
              ),
              child: const Center(
                child: Padding(
                    padding: EdgeInsets.only(left: 10, right: 10), child: Text("Enchère", style: TextStyle(color: Colors.white, fontSize: 12))),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget return_prix_bottom_card(Product snapshot) {
    if (snapshot.directProduit == "0") {
      if (snapshot.offrePrixDirect == "0") {
        return SizedBox();
      } else {
        return RichText(
          text: TextSpan(
            text: "${snapshot?.offrePrixDirect} ",
            style: const TextStyle(color: Colors.black, fontSize: 18, fontWeight: FontWeight.bold),
            children: const <TextSpan>[
              TextSpan(text: "DT", style: TextStyle(color: Colors.black, fontSize: 12, fontWeight: FontWeight.bold)),
            ],
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        );
      }
    } else {
      return RichText(
        text: TextSpan(
          text: "${snapshot?.prixProduit} ",
          style: const TextStyle(color: Colors.black, fontSize: 18, fontWeight: FontWeight.bold),
          children: const <TextSpan>[
            TextSpan(text: "DT", style: TextStyle(color: Colors.black, fontSize: 12, fontWeight: FontWeight.bold)),
          ],
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    }
  }

  void startTimer() {
    if (mounted) {
      countdownTimer = Timer.periodic(Duration(seconds: 1), (_) => setCountDown());
    }
  }

  void setCountDown() {
    if (mounted) {
      final reduceSecondsBy = 1;
      setState(() {
        final seconds = myDuration.inSeconds - reduceSecondsBy;
        if (seconds < 0) {
          countdownTimer!.cancel();
        } else {
          myDuration = Duration(seconds: seconds);
        }
      });
    }
  }

  void stopTimer() {
    setState(() => countdownTimer!.cancel());
  }
}
