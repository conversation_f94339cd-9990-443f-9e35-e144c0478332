import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/product_detail.dart';
import 'package:my_application/shimmer_effect/item_messagerie_vendeur_shimmer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:my_application/utils/string_constant.dart';
import 'dart:convert';

import 'Model/detail_one_product.dart';
import 'Model/discussion_messagerie.dart';
import 'dart:math' as math;

import 'Model/response_enchere.dart';

class DiscussionVendeur extends StatefulWidget {
  String id_product;

  String title_product;

  String nom_vendeur;

  String id_acheteur;

  String nom_acheteur;

  String id_vendeur;

  String id_discussion;

  DiscussionVendeur(
      {required this.id_product,
      required this.title_product,
      required this.nom_vendeur,
      required this.id_acheteur,
      required this.nom_acheteur,
      required this.id_vendeur,
      required this.id_discussion,
      super.key});

  @override
  State<DiscussionVendeur> createState() => _DiscussionVendeurState();
}

class _DiscussionVendeurState extends State<DiscussionVendeur> {
  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
  TextEditingController message_controller = TextEditingController();
  List<DiscussionMessagerie> listDiscussionMessagerie = [];

  @override
  void initState() {
    getDiscussionVendeur(widget.id_discussion);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: Scaffold(
        backgroundColor: ColorConstant.background_home_page,
        appBar: AppBar(
          surfaceTintColor: Colors.white,
          elevation: 3,
          shadowColor: Colors.black,
          backgroundColor: Colors.white,
          title: Text('${widget.nom_acheteur}'),
        ),
        body: Container(
          child: Stack(
            fit: StackFit.expand,
            children: [
              Positioned(
                  top: 0,
                  child: GestureDetector(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: FutureBuilder<DetailOneProduct>(
                          future: getProductDetail(),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return SizedBox(
                                height: 100,
                                width: MediaQuery.of(context).size.width - 16,
                                child: Card(
                                    child: Container(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Row(mainAxisSize: MainAxisSize.max, children: [
                                    Padding(
                                      padding: const EdgeInsets.only(right: 8.0),
                                      child: Container(
                                        height: 80,
                                        width: 80,
                                        decoration: BoxDecoration(
                                          borderRadius: const BorderRadius.only(topLeft: Radius.circular(7), topRight: Radius.circular(7)),
                                          image: DecorationImage(
                                            image: NetworkImage(
                                                "${StringConstant.base_url_image_product}${snapshot.data!.idUtilisateur}/${snapshot.data!.linksImage}"),
                                            fit: BoxFit.cover,
                                            alignment: Alignment.center,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Column(mainAxisSize: MainAxisSize.max, crossAxisAlignment: CrossAxisAlignment.start, children: [
                                      Text("${snapshot.data!.titreProduit}"),
                                      Text("DF : ${snapshot.data!.finDateProduit!}"),
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.end,
                                        mainAxisSize: MainAxisSize.max,
                                        children: [
                                          Text("${snapshot.data!.prixProduit}.000 DT"),
                                        ],
                                      ),
                                    ])
                                  ]),
                                )),
                              );
                            } else {
                              return const ItemMessagerieVendeurShimmer();
                            }
                          }),
                    ),
                    onTap: () {
                      // direction de detail  product
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) => ProductDetail(idProduct: widget.id_product, nomProduct: widget.title_product)));
                    },
                  )),
              Padding(
                  padding: const EdgeInsets.only(top: 110, bottom: 50),
                  child: listDiscussionMessagerie == []
                      ? Center(child: Text("Empty discussion"))
                      : ListView.builder(
                          itemCount: listDiscussionMessagerie.length,
                          reverse: true,
                          itemBuilder: (context, index) {
                            return ItemDiscussion(listDiscussionMessagerie[listDiscussionMessagerie.length - 1 - index]);
                          },
                        )),
              Positioned(
                  bottom: 0,
                  child: Container(
                    color: Colors.white,
                    height: 50,
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                      children: [
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.all(5.0),
                          child: TextField(
                            controller: message_controller,
                            textAlign: TextAlign.start,
                            style: const TextStyle(fontSize: 13),
                            decoration: const InputDecoration(
                              hintText: 'Envoyer un message',
                              border: OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.black),
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(10.0),
                                  bottomLeft: Radius.circular(10.0),
                                  bottomRight: Radius.circular(10.0),
                                  topRight: Radius.circular(10.0),
                                ),
                              ),
                            ),
                          ),
                        )),
                        Transform.rotate(
                          angle: -35 * math.pi / 180,
                          child: IconButton(
                              onPressed: () {
                                // action send message
                                if (message_controller.text.trim().isNotEmpty) {
                                  send_message(widget.id_product, widget.id_vendeur, widget.id_acheteur, "1", message_controller.text);
                                }
                              },
                              icon: Icon(Icons.send, color: ColorConstant.second_color)),
                        ),
                      ],
                    ),
                  ))
            ],
          ),
        ),
      ),
    );
  }

  Future<DetailOneProduct> getProductDetail() async {
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_info_product.php?id=${widget.id_product}'));
    if (res.statusCode == 200) {
      return DetailOneProduct.fromJson(json.decode(res.body));
    } else {
      return DetailOneProduct();
    }
  }

  getDiscussionVendeur(String id_discussion) async {
    List<DiscussionMessagerie> list_discussion_messagerie = [];

    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.post(Uri.parse('${StringConstant.base_url}api/get_discussion_vendeur.php'), headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }, body: {
      'token': token,
      'id_discussion': id_discussion
    });
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      list_discussion_messagerie = List<DiscussionMessagerie>.from(l.map((model) => DiscussionMessagerie.fromJson(model)));
    }
    setState(() {
      listDiscussionMessagerie = list_discussion_messagerie;
    });
  }

  Widget ItemDiscussion(DiscussionMessagerie discussion_messagerie) {
    String firstlettre = discussion_messagerie.nomUtil![0].toUpperCase();
    if (discussion_messagerie.typeUser == 1) {
      return ListTile(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.75),
                padding: EdgeInsets.all(5),
                decoration: BoxDecoration(color: ColorConstant.red_enchere, borderRadius: BorderRadius.circular(10)),
                child: Text(
                  "${discussion_messagerie.message}",
                  style: TextStyle(color: Colors.white),
                ),
                // Text("Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book"),
              ),
            ],
          ),
          subtitle: Padding(
            padding: const EdgeInsets.only(top: 2.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              mainAxisSize: MainAxisSize.max,
              children: [
                Text(
                  "${discussion_messagerie.dateDiscussionLigne!.substring(0, (discussion_messagerie.dateDiscussionLigne!.length) - 3)}",
                  style: const TextStyle(fontSize: 10, color: Colors.black45),
                ),
              ],
            ),
          ));
    } else {
      if (discussion_messagerie.proposition == "0") {
        return ListTile(
            leading: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                border: Border.all(color: ColorConstant.red_enchere),
                borderRadius: const BorderRadius.all(Radius.circular(30)),
              ),
              child: Center(
                  child: Text(
                "${firstlettre}",
                style: const TextStyle(fontWeight: FontWeight.bold),
              )),
            ),
            title: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.75),
                  padding: EdgeInsets.all(5),
                  decoration: BoxDecoration(color: Colors.black12, borderRadius: BorderRadius.circular(10)),
                  child: Text("${discussion_messagerie.message}"),
                  // Text("Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book"),
                ),
              ],
            ),
            subtitle: Padding(
              padding: const EdgeInsets.only(top: 2.0),
              child: Text(
                "${discussion_messagerie.dateDiscussionLigne!.substring(0, (discussion_messagerie.dateDiscussionLigne!.length) - 3)}",
                style: const TextStyle(fontSize: 10, color: Colors.black45),
              ),
            ));
      } else {
        if (discussion_messagerie.proposition == "1") {
          //proposition encour
          return Container(
            margin: const EdgeInsets.all(5),
            padding: const EdgeInsets.all(5),
            decoration: const BoxDecoration(
              color: Colors.black54,
              borderRadius: BorderRadius.all(Radius.circular(10)),
            ),
            child: Column(children: [
              const Row(children: [Text("Proposition client:", style: TextStyle(color: Colors.white, fontSize: 14))]),
              Row(mainAxisSize: MainAxisSize.max, mainAxisAlignment: MainAxisAlignment.center, children: [
                Text("${discussion_messagerie.message} DT", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold))
              ]),
              Row(children: [
                Expanded(
                  flex: 4,
                  child: GestureDetector(
                    child: Container(
                      margin: const EdgeInsets.all(5),
                      height: 30,
                      decoration: BoxDecoration(
                        color: ColorConstant.green_enchere,
                        borderRadius: const BorderRadius.all(Radius.circular(30)),
                      ),
                      child: const Center(child: Icon(Icons.check, color: Colors.white)),
                    ),
                    onTap: () {
                      // action accept proposition
                      Alert_dialog_accept_proposition(widget.id_product, widget.id_vendeur, widget.id_acheteur, "0", widget.id_discussion,
                          "${discussion_messagerie.idDiscussionLigne}", "${discussion_messagerie.message}");
                    },
                  ),
                ),
                Expanded(
                  flex: 4,
                  child: GestureDetector(
                    child: Container(
                      margin: const EdgeInsets.all(5),
                      height: 30,
                      decoration: BoxDecoration(
                        color: ColorConstant.red_enchere,
                        borderRadius: const BorderRadius.all(Radius.circular(30)),
                      ),
                      child: const Center(child: Icon(Icons.close, color: Colors.white)),
                    ),
                    onTap: () {
                      // action on refuse proposition
                      Alert_dialog_refus_proposition(widget.id_product, widget.id_vendeur, widget.id_acheteur, "1", widget.id_discussion,
                          "${discussion_messagerie.idDiscussionLigne}", "${discussion_messagerie.message}");
                    },
                  ),
                ),
              ])
            ]),
          );
        } else if (discussion_messagerie.proposition == "2") {
          // proposition accepter
          return Container(
              margin: const EdgeInsets.all(5),
              padding: const EdgeInsets.all(5),
              decoration: const BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.all(Radius.circular(10)),
              ),
              child: Column(children: [
                const Row(children: [Text("Proposition client:", style: TextStyle(color: Colors.white, fontSize: 14))]),
                Row(mainAxisSize: MainAxisSize.max, mainAxisAlignment: MainAxisAlignment.center, children: [
                  Text("${discussion_messagerie.message} DT", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold))
                ]),
                Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [Text("Proposition acceptée", style: TextStyle(color: Colors.white, fontSize: 14))]),
              ]));
        } else if (discussion_messagerie.proposition == "3") {
          // proposition refuser
          return Container(
              margin: const EdgeInsets.all(5),
              padding: const EdgeInsets.all(5),
              decoration: const BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.all(Radius.circular(10)),
              ),
              child: Column(children: [
                const Row(children: [Text("Proposition client:", style: TextStyle(color: Colors.white, fontSize: 14))]),
                Row(mainAxisSize: MainAxisSize.max, mainAxisAlignment: MainAxisAlignment.center, children: [
                  Text("${discussion_messagerie.message} DT", style: const TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold))
                ]),
                const Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [Text("Proposition refusée", style: TextStyle(color: Colors.white, fontSize: 14))]),
              ]));
        } else {
          return const SizedBox();
        }
      }
    }
  }

  send_message(String id_product, String id_vendeur, String id_acheteur, String typeuser, String message) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.post(Uri.parse('${StringConstant.base_url}api/send_message.php'), headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }, body: {
      'token': token,
      'id_produit': id_product,
      'id_vendeur': id_vendeur,
      'id_acheteur': id_acheteur,
      'type_user': typeuser,
      'message_content': message,
    });

    if (res.statusCode == 200) {
      ResponseEnchere _response_enchere = ResponseEnchere.fromJson(json.decode(res.body));

      if (_response_enchere.etat == "0") {
        Fluttertoast.showToast(
            msg: "${_response_enchere.message}",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            timeInSecForIosWeb: 2,
            backgroundColor: Colors.red,
            textColor: Colors.white,
            fontSize: 16.0);
      }else{
        message_controller.text = "" ;
      }
    }
    getDiscussionVendeur(widget.id_discussion);
  }

  Alert_dialog_accept_proposition(String id_produit, String id_vendeur, String id_acheteur, String etat_proposition, String id_discussion,
      String id_discussion_ligne, String montant) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Acceptation de la proposition',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            alignment: Alignment.center,
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: const Text("Êtes-vous sûr d\'accepter cette proposition !"),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                  ],
                ),
              ),
              actions: [
                Row(
                  children: [
                    Expanded(
                      flex: 5,
                      child: InkWell(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 4, top: 4, right: 2, bottom: 2),
                          child: Container(
                              height: 40,
                              decoration: BoxDecoration(color: ColorConstant.second_color, borderRadius: BorderRadius.circular(7.0)),
                              child: const Center(
                                  child: Text("Non", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                        ),
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                    Expanded(
                      flex: 5,
                      child: InkWell(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 2, top: 4, right: 4, bottom: 2),
                            child: Container(
                                height: 40,
                                decoration: BoxDecoration(color: ColorConstant.red_enchere, borderRadius: BorderRadius.circular(7.0)),
                                child: const Center(
                                    child: Text("Oui", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                          ),
                          onTap: () {
                            Navigator.of(context).pop();
                            change_etat_proposition(id_produit, id_vendeur, id_acheteur, etat_proposition, id_discussion, id_discussion_ligne, montant);
                          }),
                    ),
                  ],
                )
              ],
            );
          });
        });
  }

  Alert_dialog_refus_proposition(String id_produit, String id_vendeur, String id_acheteur, String etat_proposition, String id_discussion,
      String id_discussion_ligne, String montant) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Refus de la proposition',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            alignment: Alignment.center,
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: const Text("Êtes-vous sûr de refuser cette proposition"),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                  ],
                ),
              ),
              actions: [
                Row(
                  children: [
                    Expanded(
                      flex: 5,
                      child: InkWell(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 4, top: 4, right: 2, bottom: 2),
                          child: Container(
                              height: 40,
                              decoration: BoxDecoration(color: ColorConstant.second_color, borderRadius: BorderRadius.circular(7.0)),
                              child: const Center(
                                  child: Text("Non", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                        ),
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                    Expanded(
                      flex: 5,
                      child: InkWell(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 2, top: 4, right: 4, bottom: 2),
                            child: Container(
                                height: 40,
                                decoration: BoxDecoration(color: ColorConstant.red_enchere, borderRadius: BorderRadius.circular(7.0)),
                                child: const Center(
                                    child: Text("Oui", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                          ),
                          onTap: () {
                            Navigator.of(context).pop();
                            change_etat_proposition(id_produit, id_vendeur, id_acheteur, etat_proposition, id_discussion, id_discussion_ligne, montant);
                          }),
                    ),
                  ],
                )
              ],
            );
          });
        });
  }

  change_etat_proposition(String id_produit, String id_vendeur, String id_acheteur, String etat_proposition, String id_discussion,String id_discussion_ligne, String montant) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res ;
    if (etat_proposition == "0") {
       res = await http.post(Uri.parse('${StringConstant.base_url}api/acceptation_proposition.php'), headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      }, body: {
        'token': token,
        'id_produit': id_produit,
        'id_vendeur': id_vendeur,
        'id_acheteur': id_acheteur,
        'id_discussion': id_discussion,
        'id_discussion_ligne': id_discussion_ligne,
        'montant': montant,
      });
    } else {
       res = await http.post(Uri.parse('${StringConstant.base_url}api/refus_proposition.php'), headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      }, body: {
        'token': token,
        'id_produit': id_produit,
        'id_vendeur': id_vendeur,
        'id_acheteur': id_acheteur,
        'id_discussion': id_discussion,
        'id_discussion_ligne': id_discussion_ligne,
        'montant': montant,
      });
    }

    if (res.statusCode == 200) {
      ResponseEnchere _response_enchere = ResponseEnchere.fromJson(json.decode(res.body));
      if (_response_enchere.etat == "0") {
        Fluttertoast.showToast(
            msg: "${_response_enchere.message}",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            timeInSecForIosWeb: 2,
            backgroundColor: Colors.red,
            textColor: Colors.white,
            fontSize: 16.0);
      } else if (_response_enchere.etat == "1") {
        Fluttertoast.showToast(
            msg: "${_response_enchere.message}",
            toastLength: Toast.LENGTH_SHORT,
            gravity: ToastGravity.TOP,
            timeInSecForIosWeb: 2,
            backgroundColor: Colors.green,
            textColor: Colors.white,
            fontSize: 16.0);
      }
    }
    getDiscussionVendeur(widget.id_discussion);
  }
}
