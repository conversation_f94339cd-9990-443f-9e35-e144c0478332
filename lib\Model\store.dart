class Store {
  String? idStore;
  String? nomUtilStore;
  String? nomStore;
  String? telStore;
  String? emailStore;
  String? villeStore;
  String? delegationStore;
  String? adresseStore;
  String? boitePostaleStore;
  String? pack;
  String? imageCouvertureStore;
  String? imageProfilStore;
  String? urlStore;
  String? longitude;
  String? latitude;
  String? nbrAbonneStore;
  String? nbrArticleStore;
  String? gpsStore;
  String? messenger;
  String? whatsapp;
  String? website;
  String? facebook;
  String? instagram;
  String? tiktok;
  String? categorie;
  String? description;

  Store(
      {this.idStore,
        this.nomUtilStore,
        this.nomStore,
        this.telStore,
        this.emailStore,
        this.villeStore,
        this.delegationStore,
        this.adresseStore,
        this.boitePostaleStore,
        this.pack,
        this.imageCouvertureStore,
        this.imageProfilStore,
        this.urlStore,
        this.longitude,
        this.latitude,
        this.nbrAbonneStore,
        this.nbrArticleStore,
        this.gpsStore,
        this.messenger,
        this.whatsapp,
        this.website,
        this.facebook,
        this.instagram,
        this.tiktok,
        this.categorie,
        this.description});

  Store.fromJson(Map<String, dynamic> json) {
    idStore = json['id_store'];
    nomUtilStore = json['nom_util_store'];
    nomStore = json['nom_store'];
    telStore = json['tel_store'];
    emailStore = json['email_store'];
    villeStore = json['ville_store'];
    delegationStore = json['delegation_store'];
    adresseStore = json['adresse_store'];
    boitePostaleStore = json['boite_postale_store'];
    pack = json['pack'];
    imageCouvertureStore = json['image_couverture_store'];
    imageProfilStore = json['image_profil_store'];
    urlStore = json['url_store'];
    longitude = json['longitude'];
    latitude = json['latitude'];
    nbrAbonneStore = json['nbr_abonne_store'];
    nbrArticleStore = json['nbr_article_store'];
    gpsStore = json['gps_store'];
    messenger = json['messenger'];
    whatsapp = json['whatsapp'];
    website = json['website'];
    facebook = json['facebook'];
    instagram = json['instagram'];
    tiktok = json['tiktok'];
    categorie = json['categorie'];
    description = json['description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id_store'] = this.idStore;
    data['nom_util_store'] = this.nomUtilStore;
    data['nom_store'] = this.nomStore;
    data['tel_store'] = this.telStore;
    data['email_store'] = this.emailStore;
    data['ville_store'] = this.villeStore;
    data['delegation_store'] = this.delegationStore;
    data['adresse_store'] = this.adresseStore;
    data['boite_postale_store'] = this.boitePostaleStore;
    data['pack'] = this.pack;
    data['image_couverture_store'] = this.imageCouvertureStore;
    data['image_profil_store'] = this.imageProfilStore;
    data['url_store'] = this.urlStore;
    data['longitude'] = this.longitude;
    data['latitude'] = this.latitude;
    data['nbr_abonne_store'] = this.nbrAbonneStore;
    data['nbr_article_store'] = this.nbrArticleStore;
    data['gps_store'] = this.gpsStore;
    data['messenger'] = this.messenger;
    data['whatsapp'] = this.whatsapp;
    data['website'] = this.website;
    data['facebook'] = this.facebook;
    data['instagram'] = this.instagram;
    data['tiktok'] = this.tiktok;
    data['categorie'] = this.categorie;
    data['description'] = this.description;
    return data;
  }
}
