import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../Model/vente.dart';
import '../add_boost.dart';
import '../add_product.dart';
import '../mes_ventes.dart';
import '../product_detail.dart';
import '../utils/color_constant.dart';

class BuildItemVente extends StatefulWidget {
  final BuildContext context;
  final Vente snapshot;
  final String type;
  final VoidCallback onRemove;

  BuildItemVente({super.key, required this.snapshot, required this.context, required this.type, required this.onRemove});

  @override
  State<BuildItemVente> createState() => _BuildItemVenteState();
}

class _BuildItemVenteState extends State<BuildItemVente> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Card(
        margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              Row(mainAxisSize: MainAxisSize.max, mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Flexible(
                  child: Text("${widget.snapshot.titreProduit}", style: const TextStyle(fontSize: 18),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.start,),
                ),
                widget.type != 'vendu'
                    ? Row(
                        children: [
                          IconButton(
                              onPressed: () {
                                // redirect vers edit product
                                Navigator.push(context, MaterialPageRoute(builder: (context) =>  AddProduct(id_offre: "${widget.snapshot.id}")));
                              },
                              icon: const Icon(
                                Icons.edit,
                                color: Colors.black38,
                                size: 18,
                              )),
                          IconButton(
                              onPressed: widget.onRemove,
                              icon: const Icon(
                                Icons.close,
                                color: Colors.black38,
                                size: 18,
                              ))
                        ],
                      )
                    : const SizedBox(),
              ]),
              Row(mainAxisSize: MainAxisSize.max, mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                const Text("Date publication", style: TextStyle(fontSize: 12)),
                Expanded(child: Html(data :"${widget.snapshot.datePublication}",style: { "body": Style( fontSize: FontSize(12.0),textAlign:TextAlign.end) }))
              ]),
              Row(mainAxisSize: MainAxisSize.max, mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                const Text("Prix", style: TextStyle(fontSize: 12)),
                Text("${widget.snapshot.prix} DT", style: const TextStyle(fontSize: 14))
              ]),
              Row(mainAxisSize: MainAxisSize.max, mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                const Text("Type de l'offre", style: TextStyle(fontSize: 12)),
                Text("${widget.snapshot.typeOffre}", style: const TextStyle(fontSize: 12))
              ]),
              widget.type == 'avendre'
                  ? Row(mainAxisSize: MainAxisSize.max, mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                Expanded(
                  flex: 10,
                  child: Padding(
                    padding: const EdgeInsets.only(top :8.0),
                    child: InkWell(
                        child: Container(
                            height: 40,
                            decoration: BoxDecoration(color: ColorConstant.red_enchere, borderRadius: BorderRadius.circular(7.0)),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(horizontal: 8.0),
                              child: SizedBox(
                                child: Center(
                                    child: Text("Bi3 Asra3", style: TextStyle(color: Colors.white, fontSize: 22, fontWeight: FontWeight.normal))),
                              ),
                            )),
                        onTap: () async{
                          var result = await Navigator.push(context, MaterialPageRoute(builder: (context) => AddBoost(id_offre: '${widget.snapshot.id}')));
                          if(result == 'success') {
                            Fluttertoast.showToast(
                                msg: "Votre Boost a été effectué avec succes",
                                toastLength: Toast.LENGTH_LONG,
                                gravity: ToastGravity.TOP,
                                timeInSecForIosWeb: 5,
                                backgroundColor: Colors.green,
                                textColor: Colors.white,
                                fontSize: 16.0);
                          }
                        }),
                  ),
                ),
              ])
              : SizedBox(),
            ],
          ),
        ),
      ),
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => ProductDetail(
                      idProduct: widget.snapshot.id,
                      nomProduct: widget.snapshot.titreProduit,
                    )));
      },
    );
  }
}
