class ListAttributeType {
  String? typeAttribute;
  String? idAttribute;
  String? nomAttribute;
  List<DataAttribute>? dataAttribute;

  ListAttributeType(
      {this.typeAttribute,
        this.idAttribute,
        this.nomAttribute,
        this.dataAttribute});

  ListAttributeType.fromJson(Map<String, dynamic> json) {
    typeAttribute = json['type_attribute'];
    idAttribute = json['id_attribute'];
    nomAttribute = json['nom_attribute'];
    if (json['data_attribute'] != null) {
      dataAttribute = <DataAttribute>[];
      json['data_attribute'].forEach((v) {
        dataAttribute!.add(new DataAttribute.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type_attribute'] = this.typeAttribute;
    data['id_attribute'] = this.idAttribute;
    data['nom_attribute'] = this.nomAttribute;
    if (this.dataAttribute != null) {
      data['data_attribute'] =
          this.dataAttribute!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataAttribute {
  String? id;
  String? nomAttributeValue;

  DataAttribute({this.id, this.nomAttributeValue});

  DataAttribute.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    nomAttributeValue = json['nom_attribute_value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['nom_attribute_value'] = this.nomAttributeValue;
    return data;
  }
}