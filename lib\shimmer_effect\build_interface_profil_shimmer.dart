import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class BuildInterfaceProfilShimmer extends StatefulWidget {

  const BuildInterfaceProfilShimmer({super.key});

  @override
  State<BuildInterfaceProfilShimmer> createState() => _BuildInterfaceProfilShimmerState();
}

class _BuildInterfaceProfilShimmerState extends State<BuildInterfaceProfilShimmer> {
  @override
  Widget build(BuildContext context) {
    double item_size = MediaQuery.of(context).size.width;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [

        Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Container(
            width: 150,
            height: 30,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(topLeft: Radius.circular(4), topRight: Radius.circular(4),bottomLeft: Radius.circular(4), bottomRight: Radius.circular(4)),
            ),
            margin: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
          ),
        ),

        Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Container(
            width: item_size,
            height: 250,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(topLeft: Radius.circular(7), topRight: Radius.circular(7),bottomLeft: Radius.circular(7), bottomRight: Radius.circular(7)),
            ),
            margin: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
          ),
        ),
        Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Container(
            width: 150,
            height: 30,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(topLeft: Radius.circular(4), topRight: Radius.circular(4),bottomLeft: Radius.circular(4), bottomRight: Radius.circular(4)),
            ),
            margin: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
          ),
        ),

        Shimmer.fromColors(
          baseColor: Colors.grey.shade300,
          highlightColor: Colors.grey.shade100,
          child: Container(
            width: item_size,
            height: 500,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(topLeft: Radius.circular(7), topRight: Radius.circular(7),bottomLeft: Radius.circular(7), bottomRight: Radius.circular(7)),
            ),
            margin: const EdgeInsets.only(left: 8, right: 8, bottom: 8),
          ),
        ),
      ],
    );
  }
}



