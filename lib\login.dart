import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:my_application/Model/response_login.dart';
import 'package:my_application/forget_password.dart';
import 'package:my_application/send_code_confirm_tel.dart';
import 'package:my_application/utils/check_authenticate.dart';
import 'package:my_application/utils/color_constant.dart';
import 'dart:math' as math;
import 'package:http/http.dart' as http;
import 'package:my_application/utils/string_constant.dart';
import 'package:my_application/utils/wishlist_product_sharedprefrence.dart';
import 'package:my_application/utils/wishlist_store_sharedprefrence.dart';
import 'package:my_application/widget/show_custom_loader_dialog.dart';

class Login extends StatefulWidget {
  Login({super.key});

  @override
  State<Login> createState() => _LoginState();
}

class _LoginState extends State<Login> {

  CkeckAuthenticate ckeckAuthenticate = Get.put(CkeckAuthenticate());

  late String _tel = "";
  late String _password = "";
  bool _passwordVisible = false;
  bool _visibleerror = false;

  String errortext = "Vérifier votre numéro de téléphone et mot de passe";
  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();


  String fcm_token = "" ;

  @override
  void initState() {
    super.initState();

    try {
      FirebaseMessaging.instance.getToken().then((_fcm_token){
        fcm_token = _fcm_token!;
      });
    } catch (e) {
    }
  }


  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                IconButton(onPressed: () => Navigator.of(context).pop(), icon: Icon(Icons.keyboard_backspace, color: ColorConstant.second_color))
              ]),
            ),
            const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              Image.asset(
                'assets/images/logo.png',
                height: 40,
              )
            ]),
            const SizedBox(height: 30),
            const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [Text("Bienvenue!", style: TextStyle(color: Colors.black, fontSize: 14, decoration: TextDecoration.none))]),
            const SizedBox(height: 5),
            const Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              Text("Connectez-vous pour gérer votre compte.", style: TextStyle(color: Colors.black45, fontSize: 12, decoration: TextDecoration.none))
            ]),
            const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Padding(
                    padding: EdgeInsets.only(right: 30, left: 30),
                    child: TextFormField(
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        fillColor: Colors.white,
                        labelText: "Numéro de téléphone",
                        labelStyle: TextStyle(fontSize: 14, color: Colors.black),
                        prefixIcon: Icon(
                          Icons.phone,
                          color: Colors.black,
                        ),
                        filled: true,
                      ),
                      autofocus: false,
                      cursorColor: Colors.black,
                      keyboardType: TextInputType.phone,
                      textInputAction: TextInputAction.next,
                      onChanged: (value) => _tel = value,
                    ),
                  ))
            ]),
            const SizedBox(height: 10),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                  width: MediaQuery.of(context).size.width,
                  child: Padding(
                    padding: EdgeInsets.only(right: 30, left: 30),
                    child: TextFormField(
                      obscureText: !_passwordVisible,
                      decoration: InputDecoration(
                        prefixIcon: const Icon(Icons.lock_outlined),
                        border: const OutlineInputBorder(),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.black),
                        ),
                        labelText: 'Mot de passe',
                        labelStyle: TextStyle(fontSize: 14, color: Colors.black),
                        suffixIcon: IconButton(
                          icon: Icon(
                            // Based on passwordVisible state choose the icon
                            _passwordVisible ? Icons.visibility : Icons.visibility_off,
                            color: Colors.black45,
                          ),
                          onPressed: () {
                            // Update the state i.e. toogle the state of passwordVisible variable
                            setState(() {
                              _passwordVisible = !_passwordVisible;
                            });
                          },
                        ),
                      ),
                      validator: (value) => value == "" ? 'Entrer votre password' : null,
                      onChanged: (value) => _password = value,
                      autofocus: false,
                      cursorColor: Colors.black,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.done,
                    ),
                  ))
            ]),
            _visibleerror == true
                ? Padding(
                    padding: const EdgeInsets.only(left: 30.0, right: 30.0, top: 10.0, bottom: 10.0),
                    child: Text("${errortext}", style: const TextStyle(color: Colors.red, fontSize: 12, decoration: TextDecoration.none),softWrap: true,textAlign: TextAlign.center),
                  )
                : const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.end, children: [
              GestureDetector(
                child: Padding(
                  padding: EdgeInsets.only(right: 30, left: 30),
                  child: Text("Mot de passe oublié?", style: TextStyle(color: Colors.black, fontSize: 12, decoration: TextDecoration.none)),
                ),
                onTap: (){
                  Navigator.push(context, MaterialPageRoute(builder: (context) => ForgetPassword()));
                },
              )
            ]),
            const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                height: 50,
                width: MediaQuery.of(context).size.width,
                child: Padding(
                  padding: const EdgeInsets.only(right: 30, left: 30),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorConstant.red_enchere,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    onPressed: () {
                      ShowCustomLoaderDialog().showCustomDialog(context);
                      login();
                    },
                    child: const Center(child: Text("S\'identifier", style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 13))),
                  ),
                ),
              )
            ]),
            const SizedBox(height: 20),
            const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [Text("Vous n'avez pas de compte?", style: TextStyle(color: Colors.black45, fontSize: 12, decoration: TextDecoration.none))]),
            const SizedBox(height: 20),
            Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              SizedBox(
                height: 50,
                width: MediaQuery.of(context).size.width,
                child: Padding(
                  padding: const EdgeInsets.only(right: 30, left: 30),
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorConstant.second_color,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                    ),
                    onPressed: () {
                      Navigator.push(context, MaterialPageRoute(builder: (context) => SendCodeConfirmTel()));
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Transform(transform: Matrix4.rotationY(math.pi), child: const Icon(Icons.gavel, color: Colors.white)),
                        const Center(child: Text("Inscrivez vous gratuitment", style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 13))),
                      ],
                    ),
                  ),
                ),
              )
            ]),
            const SizedBox(height: 40),
            const Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              Text("© CERRO SOFT - All rights Resereved", style: TextStyle(color: Colors.black45, fontSize: 12, decoration: TextDecoration.none))
            ]),
            const SizedBox(height: 20),
          ]),
        ),
      ),
    );
  }

  void login() async {
    try {

      var res = await http.post(Uri.parse('${StringConstant.base_url}api/login.php'),
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: {
            'user_tel': _tel,
            'user_password': _password,
            'token_fcm': fcm_token,
          });
      if (res.statusCode == 200) {
        ResponseLogin responselogin = ResponseLogin.fromJson(jsonDecode(res.body));
        if (responselogin.etat == "0") {

          _prefs.then((data) => {
                data.setString('user_tel', _tel),
                data.setString('user_password', _password),
                data.setString('id_user', "${responselogin.iduser}"),
                data.setString('user_token', "${responselogin.token}")
              });
          ckeckAuthenticate.loggedin = true.obs ;

          WishlistStoreSharedprefrence().get_list_wishlist_store();
          WishlistProductSharedprefrence().get_list_wishlist_Product();
          
          await Future.delayed(const Duration(seconds: 1));

          Navigator.of(context).pop();
          Navigator.of(context).pop();
        } else {
          Navigator.of(context).pop();
          setState(() {
            errortext = "Vérifier votre numéro de téléphone et mot de passe";
            _visibleerror = true;
          });
        }
      } else {
        Navigator.of(context).pop();
        errortext = "Une erreur s'est produite, contactez le support";
        _visibleerror = true;
      }
    } on Exception catch (e) {
      Navigator.of(context).pop();
      print(e);
      throw Exception("Error on server");
    }
  }
}
