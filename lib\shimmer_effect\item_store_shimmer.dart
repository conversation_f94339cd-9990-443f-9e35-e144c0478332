import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ItemStoreShimmer extends StatefulWidget {
  const ItemStoreShimmer({super.key});

  @override
  State<ItemStoreShimmer> createState() => _ItemStoreShimmerState();
}

class _ItemStoreShimmerState extends State<ItemStoreShimmer> {
  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(
        height: 150,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(7)),
        ),
        margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
      ),
    );
  }
}
