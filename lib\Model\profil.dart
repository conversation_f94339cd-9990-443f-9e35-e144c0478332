class Profil {
  String? id;
  String? nom;
  String? gendre;
  String? dateNais;
  String? email;
  String? imgAdmin;
  String? etat;
  String? numMembre;
  String? typeMembre;
  String? situation;
  String? nomUtil;
  String? numTel;
  String? ville;
  String? delegation;
  String? adresse;
  String? boitePostale;
  String? profileComplet;
  String? emailConf;
  String? telConf;
  String? nomConf;
  String? nature;
  String? raisonSocial;
  String? representantLegal;
  String? identifiantUnique;
  String? logoStore;
  String? bannerStore;
  String? urlStore;
  String? pack;
  String? packDateDebut;
  String? packDateFin;
  String? gps;
  String? messenger;
  String? whatsapp;
  String? website;
  String? facebook;
  String? instagram;
  String? tiktok;
  String? categorie;
  String? description;

  Profil(
      {this.id,
        this.nom,
        this.gendre,
        this.dateNais,
        this.email,
        this.imgAdmin,
        this.etat,
        this.numMembre,
        this.typeMembre,
        this.situation,
        this.nomUtil,
        this.numTel,
        this.ville,
        this.delegation,
        this.adresse,
        this.boitePostale,
        this.profileComplet,
        this.emailConf,
        this.telConf,
        this.nomConf,
        this.nature,
        this.raisonSocial,
        this.representantLegal,
        this.identifiantUnique,
        this.logoStore,
        this.bannerStore,
        this.urlStore,
        this.pack,
        this.packDateDebut,
        this.packDateFin,
        this.gps,
        this.messenger,
        this.whatsapp,
        this.website,
        this.facebook,
        this.instagram,
        this.tiktok,
        this.categorie,
        this.description});

  Profil.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    nom = json['nom'];
    gendre = json['Gendre'];
    dateNais = json['date_nais'];
    email = json['email'];
    imgAdmin = json['img_admin'];
    etat = json['etat'];
    numMembre = json['num_membre'];
    typeMembre = json['type_membre'];
    situation = json['situation'];
    nomUtil = json['nom_util'];
    numTel = json['num_tel'];
    ville = json['ville'];
    delegation = json['delegation'];
    adresse = json['adresse'];
    boitePostale = json['boite_postale'];
    profileComplet = json['profile_complet'];
    emailConf = json['email_conf'];
    telConf = json['tel_conf'];
    nomConf = json['nom_conf'];
    nature = json['nature'];
    raisonSocial = json['raison_social'];
    representantLegal = json['representant_legal'];
    identifiantUnique = json['identifiant_unique'];
    logoStore = json['logo_store'];
    bannerStore = json['banner_store'];
    urlStore = json['url_store'];
    pack = json['pack'];
    packDateDebut = json['pack_date_debut'];
    packDateFin = json['pack_date_fin'];
    gps = json['gps'];
    messenger = json['messenger'];
    whatsapp = json['whatsapp'];
    website = json['website'];
    facebook = json['facebook'];
    instagram = json['instagram'];
    tiktok = json['tiktok'];
    categorie = json['categorie'];
    description = json['description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['nom'] = this.nom;
    data['Gendre'] = this.gendre;
    data['date_nais'] = this.dateNais;
    data['email'] = this.email;
    data['img_admin'] = this.imgAdmin;
    data['etat'] = this.etat;
    data['num_membre'] = this.numMembre;
    data['type_membre'] = this.typeMembre;
    data['situation'] = this.situation;
    data['nom_util'] = this.nomUtil;
    data['num_tel'] = this.numTel;
    data['ville'] = this.ville;
    data['delegation'] = this.delegation;
    data['adresse'] = this.adresse;
    data['boite_postale'] = this.boitePostale;
    data['profile_complet'] = this.profileComplet;
    data['email_conf'] = this.emailConf;
    data['tel_conf'] = this.telConf;
    data['nom_conf'] = this.nomConf;
    data['nature'] = this.nature;
    data['raison_social'] = this.raisonSocial;
    data['representant_legal'] = this.representantLegal;
    data['identifiant_unique'] = this.identifiantUnique;
    data['logo_store'] = this.logoStore;
    data['banner_store'] = this.bannerStore;
    data['url_store'] = this.urlStore;
    data['pack'] = this.pack;
    data['pack_date_debut'] = this.packDateDebut;
    data['pack_date_fin'] = this.packDateFin;
    data['gps'] = this.gps;
    data['messenger'] = this.messenger;
    data['whatsapp'] = this.whatsapp;
    data['website'] = this.website;
    data['facebook'] = this.facebook;
    data['instagram'] = this.instagram;
    data['tiktok'] = this.tiktok;
    data['categorie'] = this.categorie;
    data['description'] = this.description;
    return data;
  }
}
