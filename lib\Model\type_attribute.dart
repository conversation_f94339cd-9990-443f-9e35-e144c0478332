import 'data_attribute.dart';

class TypeAttribute {
  String? typeAttribute;
  String? idAttribute;
  String? nomAttribute;
  List<DataAttribute>? dataAttribute;

  TypeAttribute(
      {this.typeAttribute,
        this.idAttribute,
        this.nomAttribute,
        this.dataAttribute});

  TypeAttribute.fromJson(Map<String, dynamic> json) {
    typeAttribute = json['type_attribute'];
    idAttribute = json['id_attribute'];
    nomAttribute = json['nom_attribute'];
    if (json['data_attribute'] != null) {
      dataAttribute = <DataAttribute>[];
      json['data_attribute'].forEach((v) {
        dataAttribute!.add(new DataAttribute.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type_attribute'] = this.typeAttribute;
    data['id_attribute'] = this.idAttribute;
    data['nom_attribute'] = this.nomAttribute;
    if (this.dataAttribute != null) {
      data['data_attribute'] =
          this.dataAttribute!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}


