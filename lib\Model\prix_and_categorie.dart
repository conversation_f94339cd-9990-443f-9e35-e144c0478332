import 'Categorie.dart';

class PrixAndCategorie {
  String? prixProduitMax;
  String? prixProduitMin;
  List<Categorie>? categoriePath;

  PrixAndCategorie(
      {this.prixProduitMax, this.prixProduitMin, this.categoriePath});

  PrixAndCategorie.fromJson(Map<String, dynamic> json) {
    prixProduitMax = json['prix_produit_max'];
    prixProduitMin = json['prix_produit_min'];
    if (json['categorie_path'] != null) {
      categoriePath = <Categorie>[];
      json['categorie_path'].forEach((v) {
        categoriePath!.add(new Categorie.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['prix_produit_max'] = this.prixProduitMax;
    data['prix_produit_min'] = this.prixProduitMin;
    if (this.categoriePath != null) {
      data['categorie_path'] =
          this.categoriePath!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

