import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import '../Model/jeton_operations.dart';
import '../Model/vente.dart';
import '../add_product.dart';
import '../mes_ventes.dart';
import '../product_detail.dart';
import '../utils/color_constant.dart';

class BuildItemTransaction extends StatefulWidget {
  final BuildContext context;
  final JetonOperations snapshot;

  BuildItemTransaction({super.key, required this.snapshot, required this.context});

  @override
  State<BuildItemTransaction> createState() => _BuildItemTransactionState();
}

class _BuildItemTransactionState extends State<BuildItemTransaction> {
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 5,
      child: SizedBox(
          height: 70,
          child: Padding(
            padding: const EdgeInsets.all(5.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: Image(image: const AssetImage('assets/images/diamant.png'), height: 40, color: ColorConstant.second_color),
                ),
                Expanded(
                  child: Column(crossAxisAlignment: CrossAxisAlignment.start, mainAxisAlignment: MainAxisAlignment.center, children: [
                    Text('${widget.snapshot.libelleOp}',style: TextStyle(fontSize: 13)),
                    SizedBox(height: 2),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('${widget.snapshot.nbreJetons} Diamants',style: TextStyle(fontSize: 18)),
                        Row(
                          children: [
                            Text("${widget.snapshot.date}",style: TextStyle(color: Colors.black45)),
                            Icon(Icons.access_time,color: Colors.black45,size: 18,),
                          ],
                        ),

                      ],
                    )
                  ],),
                ),
              ],
            ),
          )
      ),
    );
  }
}
