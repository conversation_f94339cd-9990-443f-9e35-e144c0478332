class JetonOperations {
  String? utilisateurId;
  String? libelleOp;
  String? produitId;
  String? typeOp;
  String? nbreJetons;
  String? idBoostAnnonce;
  String? date;

  JetonOperations(
      {this.utilisateurId,
        this.libelleOp,
        this.produitId,
        this.typeOp,
        this.nbreJetons,
        this.idBoostAnnonce,
        this.date});

  JetonOperations.fromJson(Map<String, dynamic> json) {
    utilisateurId = json['utilisateurId'];
    libelleOp = json['libelleOp'];
    produitId = json['produitId'];
    typeOp = json['typeOp'];
    nbreJetons = json['nbreJetons'];
    idBoostAnnonce = json['id_boost_annonce'];
    date = json['date'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['utilisateurId'] = this.utilisateurId;
    data['libelleOp'] = this.libelleOp;
    data['produitId'] = this.produitId;
    data['typeOp'] = this.typeOp;
    data['nbreJetons'] = this.nbreJetons;
    data['id_boost_annonce'] = this.idBoostAnnonce;
    data['date'] = this.date;
    return data;
  }
}