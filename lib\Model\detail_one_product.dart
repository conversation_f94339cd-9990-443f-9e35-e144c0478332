class DetailOneProduct {
  String? id;
  String? titreProduit;
  String? produitTypeProduit;
  String? categorieProduit;
  String? etatProduit;
  String? sousTitreProduit;
  String? descriptionProduit;
  String? prixProduit;
  String? offrePrixDirect;
  String? debutProduit;
  String? debutDateProduit;
  String? finDateProduit;
  String? incrementProduit;
  String? dateInsertProduit;
  String? statutProduit;
  String? idUtilisateur;
  String? typeVenteProduit;
  String? nbrEnchereProduit;
  String? dernierEnchereProduit;
  String? villeProduit;
  String? terminerProduit;
  String? idAcheteurProduit;
  String? dateAchatDirect;
  String? directProduit;
  String? propositionPrixProduit;
  List<String>? linksImage;
  List<Attribute>? attribute;
  String? nomVendeur;
  String? telVendeur;
  String? nomVille;
  String? packVendeur;
  String? linkShare;
  String? livraisonProduit;

  DetailOneProduct(
      {this.id,
        this.titreProduit,
        this.produitTypeProduit,
        this.categorieProduit,
        this.etatProduit,
        this.sousTitreProduit,
        this.descriptionProduit,
        this.prixProduit,
        this.offrePrixDirect,
        this.debutProduit,
        this.debutDateProduit,
        this.finDateProduit,
        this.incrementProduit,
        this.dateInsertProduit,
        this.statutProduit,
        this.idUtilisateur,
        this.typeVenteProduit,
        this.nbrEnchereProduit,
        this.dernierEnchereProduit,
        this.villeProduit,
        this.terminerProduit,
        this.idAcheteurProduit,
        this.dateAchatDirect,
        this.directProduit,
        this.propositionPrixProduit,
        this.linksImage,
        this.attribute,
        this.nomVendeur,
        this.telVendeur,
        this.nomVille,
        this.packVendeur,
        this.linkShare,
        this.livraisonProduit});

  DetailOneProduct.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    titreProduit = json['titre_produit'];
    produitTypeProduit = json['produit_type_produit'];
    categorieProduit = json['categorie_produit'];
    etatProduit = json['etat_produit'];
    sousTitreProduit = json['sous_titre_produit'];
    descriptionProduit = json['description_produit'];
    prixProduit = json['prix_produit'];
    offrePrixDirect = json['offre_prix_direct'];
    debutProduit = json['debut_produit'];
    debutDateProduit = json['debut_date_produit'];
    finDateProduit = json['fin_date_produit'];
    incrementProduit = json['increment_produit'];
    dateInsertProduit = json['date_insert_produit'];
    statutProduit = json['statut_produit'];
    idUtilisateur = json['id_utilisateur'];
    typeVenteProduit = json['type_vente_produit'];
    nbrEnchereProduit = json['nbr_enchere_produit'];
    dernierEnchereProduit = json['dernier_enchere_produit'];
    villeProduit = json['ville_produit'];
    terminerProduit = json['terminer_produit'];
    idAcheteurProduit = json['id_acheteur_produit'];
    dateAchatDirect = json['date_achat_direct'];
    directProduit = json['direct_produit'];
    propositionPrixProduit = json['proposition_prix_produit'];
    linksImage = json['links_image'].cast<String>();
    if (json['attribute'] != null) {
      attribute = <Attribute>[];
      json['attribute'].forEach((v) {
        attribute!.add(new Attribute.fromJson(v));
      });
    }
    nomVendeur = json['nom_vendeur'];
    telVendeur = json['tel_vendeur'];
    nomVille = json['nom_ville'];
    packVendeur = json['pack_vendeur'];
    linkShare = json['link_share'];
    livraisonProduit = json['livraison_produit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['titre_produit'] = this.titreProduit;
    data['produit_type_produit'] = this.produitTypeProduit;
    data['categorie_produit'] = this.categorieProduit;
    data['etat_produit'] = this.etatProduit;
    data['sous_titre_produit'] = this.sousTitreProduit;
    data['description_produit'] = this.descriptionProduit;
    data['prix_produit'] = this.prixProduit;
    data['offre_prix_direct'] = this.offrePrixDirect;
    data['debut_produit'] = this.debutProduit;
    data['debut_date_produit'] = this.debutDateProduit;
    data['fin_date_produit'] = this.finDateProduit;
    data['increment_produit'] = this.incrementProduit;
    data['date_insert_produit'] = this.dateInsertProduit;
    data['statut_produit'] = this.statutProduit;
    data['id_utilisateur'] = this.idUtilisateur;
    data['type_vente_produit'] = this.typeVenteProduit;
    data['nbr_enchere_produit'] = this.nbrEnchereProduit;
    data['dernier_enchere_produit'] = this.dernierEnchereProduit;
    data['ville_produit'] = this.villeProduit;
    data['terminer_produit'] = this.terminerProduit;
    data['id_acheteur_produit'] = this.idAcheteurProduit;
    data['date_achat_direct'] = this.dateAchatDirect;
    data['direct_produit'] = this.directProduit;
    data['proposition_prix_produit'] = this.propositionPrixProduit;
    data['links_image'] = this.linksImage;
    if (this.attribute != null) {
      data['attribute'] = this.attribute!.map((v) => v.toJson()).toList();
    }
    data['nom_vendeur'] = this.nomVendeur;
    data['tel_vendeur'] = this.telVendeur;
    data['nom_ville'] = this.nomVille;
    data['pack_vendeur'] = this.packVendeur;
    data['link_share'] = this.linkShare;
    data['livraison_produit'] = this.livraisonProduit;
    return data;
  }
}

class Attribute {
  String? attributeName;
  String? attributeValue;

  Attribute({this.attributeName, this.attributeValue});

  Attribute.fromJson(Map<String, dynamic> json) {
    attributeName = json['attribute_name'];
    attributeValue = json['attribute_value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['attribute_name'] = this.attributeName;
    data['attribute_value'] = this.attributeValue;
    return data;
  }
}
