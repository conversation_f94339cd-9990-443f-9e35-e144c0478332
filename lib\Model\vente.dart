class Vente {
  String? id;
  String? titreProduit;
  String? datePublication;
  String? prix;
  String? nomAcheteur;
  String? typeOffre;
  String? exist;

  Vente(
      {this.id,
        this.titreProduit,
        this.datePublication,
        this.prix,
        this.nomAcheteur,
        this.typeOffre,
        this.exist});

  Vente.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    titreProduit = json['titre_produit'];
    datePublication = json['date_publication'];
    prix = json['prix'];
    nomAcheteur = json['nom_acheteur'];
    typeOffre = json['type_offre'];
    exist = json['exist'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['titre_produit'] = this.titreProduit;
    data['date_publication'] = this.datePublication;
    data['prix'] = this.prix;
    data['nom_acheteur'] = this.nomAcheteur;
    data['type_offre'] = this.typeOffre;
    data['exist'] = this.exist;
    return data;
  }
}
