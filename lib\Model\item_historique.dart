class ItemHistorique {
  String? nomUtilUtilisateur;
  String? prixEnchere;
  String? dateEnchere;

  ItemHistorique({this.nomUtilUtilisateur, this.prixEnchere, this.dateEnchere});

  ItemHistorique.fromJson(Map<String, dynamic> json) {
    nomUtilUtilisateur = json['nom_util_utilisateur'];
    prixEnchere = json['prix_enchere'];
    dateEnchere = json['date_enchere'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['nom_util_utilisateur'] = this.nomUtilUtilisateur;
    data['prix_enchere'] = this.prixEnchere;
    data['date_enchere'] = this.dateEnchere;
    return data;
  }
}
