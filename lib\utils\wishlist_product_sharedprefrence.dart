import 'dart:convert';

import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/Model/product.dart';
import 'package:my_application/utils/string_constant.dart';


class WishlistProductSharedprefrence extends GetxController {
  final GetStorage box = GetStorage();
  List<String> ListProduct = [];

  WishlistProductSharedprefrence();

  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();

  List<String> WishlistProductSharedprefrence_data() {
    if (box.hasData('wishlist_product')) {
      ListProduct = List<String>.from(box.read('wishlist_product'));
      if (ListProduct.isNotEmpty) {
        return ListProduct;
      } else {
        return [];
      }
    } else {
      return [];
    }
  }

  void add_to_WishlistProductSharedprefrence_data(String id_product) {
    if (box.hasData('wishlist_product')) {
      ListProduct = List<String>.from(box.read('wishlist_product'));
      ListProduct.add(id_product);
      box.write('wishlist_product', ListProduct);
    } else {
      ListProduct.add(id_product);
      box.write('wishlist_product', ListProduct);
    }
    call_add_to_wishlist_product_server(id_product);
  }

  void remove_from_WishlistProductSharedprefrence_data(String id_product) {
    if (box.hasData('wishlist_product')) {
      ListProduct = List<String>.from(box.read('wishlist_product'));
      ListProduct.remove(id_product);
      box.write('wishlist_product', ListProduct);
    } else {
      ListProduct.add(id_product);
      box.write('wishlist_product', ListProduct);
    }
    call_remove_from_wishlist_product_server(id_product);
  }

  call_add_to_wishlist_product_server(id_product) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    await http.get(Uri.parse('${StringConstant.base_url}api/add_offre_wishlist.php?token=${token}&id_product=${id_product}'));
  }

  call_remove_from_wishlist_product_server(id_product) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    await http.get(Uri.parse('${StringConstant.base_url}api/remove_offre_wishlist.php?token=${token}&id_product=${id_product}'));
  }

  get_list_wishlist_Product() async {
    List<String> list_id_product = [];
    box.write('wishlist_product', list_id_product);
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_wishlist.php?token=${token}'));
    Iterable l = json.decode(res.body);
    List<Product> ListProduct = List<Product>.from(l.map((model) => Product.fromJson(model)));
    ListProduct.forEach((Product) {
      list_id_product.add(Product.id!);
    });
    box.write('wishlist_product', list_id_product);
  }

  bool check_exist_in_whishlist(id_product){
    if (box.hasData('wishlist_product')) {
      ListProduct = List<String>.from(box.read('wishlist_product'));
      if (ListProduct.contains(id_product)) {
        return true;
      } else {
        return false;
      }
    }else{
      return false;
    }

    return true ;
  }
  void remove_all_WishlistProductSharedprefrence_data(){
    box.erase();
  }
}
