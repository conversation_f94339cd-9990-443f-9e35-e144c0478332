class DiscussionMessagerie {
  int? typeUser;
  String? nomUtil;
  String? dateDiscussionLigne;
  String? proposition;
  String? idDiscussion;
  String? idDiscussionLigne;
  String? urlPhotoProfil;
  String? message;

  DiscussionMessagerie(
      {this.typeUser,
        this.nomUtil,
        this.dateDiscussionLigne,
        this.proposition,
        this.idDiscussion,
        this.idDiscussionLigne,
        this.urlPhotoProfil,
        this.message});

  DiscussionMessagerie.fromJson(Map<String, dynamic> json) {
    typeUser = json['type_user'];
    nomUtil = json['nom_util'];
    dateDiscussionLigne = json['date_discussion_ligne'];
    proposition = json['proposition'];
    idDiscussion = json['id_discussion'];
    idDiscussionLigne = json['id_discussion_ligne'];
    urlPhotoProfil = json['url_photo_profil'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type_user'] = this.typeUser;
    data['nom_util'] = this.nomUtil;
    data['date_discussion_ligne'] = this.dateDiscussionLigne;
    data['proposition'] = this.proposition;
    data['id_discussion'] = this.idDiscussion;
    data['id_discussion_ligne'] = this.idDiscussionLigne;
    data['url_photo_profil'] = this.urlPhotoProfil;
    data['message'] = this.message;
    return data;
  }
}