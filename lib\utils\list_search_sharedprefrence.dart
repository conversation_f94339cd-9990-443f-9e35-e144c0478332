import 'dart:convert';

import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:get_storage/get_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ListSearchSharedprefrence extends GetxController{
  final GetStorage box = GetStorage();
  List<String> ListSearch = [] ;
  ListSearchSharedprefrence();

  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();

  List<String> ListSearchSharedprefrence_data(){
    try {
    if(box.hasData('list_search')) {
      ListSearch = List<String>.from(box.read('list_search')) ;
      if (ListSearch.isNotEmpty) {
        return ListSearch ;
      } else {
        return [];
      }
    }else{
      return [];
    }
    } catch (e) {
      // No specified type, handles all
      print('Something really unknown: $e');
    }

    return [] ;
  }

  void add_to_ListSearchSharedprefrence_data(String item_search){
    if(box.hasData('list_search')) {
      ListSearch = List<String>.from(box.read('list_search'));
      for(int i = 0 ; i < ListSearch.length ; i++){
        if(ListSearch[i] == item_search){
          ListSearch.removeAt(i) ;
        }
      };
      if(ListSearch.length >= 10){
        ListSearch.removeLast();
        ListSearch.insert(0,item_search);
      }else{
        ListSearch.insert(0,item_search);
      }

      box.write('list_search', ListSearch);
    }else{
      ListSearch.add(item_search);
      box.write('list_search', ListSearch);
    }
  }

  void remove_from_ListSearchSharedprefrence_data(String item_search){
    if(box.hasData('list_search')) {
      ListSearch = List<String>.from(box.read('list_search'));
      ListSearch.remove(item_search);
      box.write('list_search', ListSearch);
    }
  }


  void remove_all_ListSearchSharedprefrence_data(){
    box.erase();
  }

}