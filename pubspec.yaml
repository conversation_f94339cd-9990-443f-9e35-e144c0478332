name: my_application
description: "<PERSON><PERSON><PERSON> enchere"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.4.1 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  flutter_localization: ^0.2.0
  cupertino_icons: ^1.0.8
  flutter_native_splash: ^2.3.1
  http: ^1.1.0
  convex_bottom_bar: ^3.2.0
  url_launcher: ^6.1.12
  share_plus: ^7.0.2
  carousel_slider: ^5.0.0
  dots_indicator: ^3.0.0
  flutter_html: ^3.0.0-alpha.3
  pinch_zoom_release_unzoom: ^1.0.1
  shared_preferences: ^2.2.1
  #webview_flutter: ^2.0.12
  #webview_flutter: ^4.2.4
  shimmer: ^3.0.0
  get: ^4.6.5
  get_storage: ^2.1.1
  flutter_tags: "^1.0.0-nullsafety.1"
  intl: ^0.19.0
  image_picker: ^1.1.2
  firebase_database: ^11.0.1
  firebase_core: ^3.1.0
  fluttertoast: ^8.2.6
  flutter_xlider: ^3.5.0
  webview_flutter: ^4.8.0
  permission_handler: ^11.3.1
  file_picker: ^8.0.6
  app_links: ^6.1.4
  firebase_messaging: ^15.0.3
  flutter_local_notifications: ^17.2.1+2
  firebase_crashlytics: ^4.0.4
  lottie: ^2.6.0
  countup: ^0.1.4
  audioplayers: ^6.1.0


# flutter splash screen
flutter_native_splash:
  color: "#ffffff"
  image: assets/images/launcher_logo.png
  android_12:
    color: "#ffffff"
    image: assets/images/launcher_logo.png


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/images/
    - assets/




  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For

  fonts:
    - family: Araboto
      fonts:
        - asset: fonts/arabotobold.ttf
        - asset: fonts/arabotonormal.ttf
        - asset: fonts/arabotothin.ttf


  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
