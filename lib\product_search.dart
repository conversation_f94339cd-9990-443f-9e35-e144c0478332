import 'package:flutter/material.dart';
import 'package:my_application/product_list.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:my_application/utils/list_search_sharedprefrence.dart';


class ProductSearch extends StatefulWidget {
  const ProductSearch({super.key});

  @override
  State<ProductSearch> createState() => _ProductSearchState();
}

class _ProductSearchState extends State<ProductSearch> {
  List<String> item_search = [];
  TextEditingController _controller_search = TextEditingController();

  @override
  void initState() {
    init_data() ;
    super.initState();
  }
  init_data(){
    setState(() {
      item_search = ListSearchSharedprefrence().ListSearchSharedprefrence_data();
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: Scaffold(
          backgroundColor: ColorConstant.background_home_page,
          appBar: AppBar(
            surfaceTintColor: Colors.white,
            elevation: 3,
            shadowColor: Colors.black,
            backgroundColor: Colors.white,
            title: Container(
                color: Colors.white,
                child: Center(
                    child: TextField(
                        controller: _controller_search,
                        textAlign: TextAlign.center,
                        style: const TextStyle(fontSize: 11),
                        textAlignVertical: TextAlignVertical.center,
                        autofocus: true,
                        decoration: const InputDecoration(
                          hintText: "Recherche produit",
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.only(bottom: 18.0),
                          filled: true,
                          fillColor: Colors.white
                        ),

                ))),
            actions: [
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: () {
                  String text_search = _controller_search.text ;
                  if(text_search.trim().isNotEmpty ) {
                    ListSearchSharedprefrence().add_to_ListSearchSharedprefrence_data("${text_search}");
                    Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => ProductList("${text_search}", "", order: "1")));
                  }
                  //Navigator.push(context, MaterialPageRoute(builder: (context) => StoreDetail(idStore: "1", nomUtilStore: "test seif")));
                },
              )
            ],
          ),
          body: item_search != []
              ? ListView.separated(
                  itemCount: item_search.length,
                  itemBuilder: (BuildContext context, int index) {
                    return ListTile(
                      title: Text(item_search[index]),
                      leading: const Icon(Icons.search),
                      trailing: IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: (){
                          ListSearchSharedprefrence().remove_from_ListSearchSharedprefrence_data(item_search[index]);
                          init_data() ;
                        } ,
                      ),
                      onTap: (){
                        Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => ProductList("${item_search[index]}", "", order: "1")));
                      },
                    );
                  },
                  separatorBuilder: (context, index) {
                    return const Divider(height: 1);
                  },
                )
              : const SizedBox(child: Center(child: Text('Aucune information')))),
    );
  }
}
