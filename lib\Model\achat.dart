class Achat {
  String? id;
  String? titreProduit;
  String? date;
  String? prix;
  String? nom;
  String? exist;

  Achat({this.id, this.titreProduit, this.date, this.prix, this.nom, this.exist});

  Achat.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    titreProduit = json['titre_produit'];
    date = json['date'];
    prix = json['prix'];
    nom = json['nom'];
    exist = json['exist'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['titre_produit'] = this.titreProduit;
    data['date'] = this.date;
    data['prix'] = this.prix;
    data['nom'] = this.nom;
    data['exist'] = this.exist;
    return data;
  }
}
