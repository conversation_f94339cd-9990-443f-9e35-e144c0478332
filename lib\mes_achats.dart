import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:my_application/shimmer_effect/item_achat_shimmer.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:my_application/utils/string_constant.dart';
import 'package:my_application/widget/build_item_achat.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

import 'Model/achat.dart';

class MesAchats extends StatefulWidget {
  MesAchats({super.key});

  @override
  State<MesAchats> createState() => _MesAchatsState();
}

class _MesAchatsState extends State<MesAchats> {
  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();

  bool loading_achat = true;

  bool loading_enchere = true;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: DefaultTabController(
        length: 2,
        child: Scaffold(
          backgroundColor: ColorConstant.background_home_page,
          appBar: AppBar(
              surfaceTintColor: Colors.white,
              elevation: 3,
              shadowColor: Colors.black,
              backgroundColor: Colors.white,
              title: const Text('Mes achats'),
              bottom: const TabBar(
                indicatorSize: TabBarIndicatorSize.tab,
                tabs: [
                  Tab(text: "MES ENCHÈRES"),
                  Tab(text: "ACHETÉS"),
                ],
              )),
          body: Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: TabBarView(
              children: [
                FutureBuilder<List<Achat>>(
                    future: get_mes_achats('enchere'),
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) {
                        return ListView.builder(
                            shrinkWrap: true,
                            scrollDirection: Axis.vertical,
                            itemCount: 8,
                            itemBuilder: (context, int index) {
                              return const ItemAchatShimmer();
                            });
                      } else {
                        if (snapshot.data?.length == 0) {
                          return const Center(child: Text('Aucune information'));
                        } else {
                          return ListView.builder(
                              shrinkWrap: true,
                              scrollDirection: Axis.vertical,
                              itemCount: snapshot.data?.length,
                              itemBuilder: (context, int index) {
                                return BuildItemAchat(snapshot: snapshot.data!.elementAt(index), context: context);
                              });
                        }
                      }
                    }),
                FutureBuilder<List<Achat>>(
                    future: get_mes_achats('achat'),
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) {
                        return ListView.builder(
                            shrinkWrap: true,
                            scrollDirection: Axis.vertical,
                            itemCount: 8,
                            itemBuilder: (context, int index) {
                              return const ItemAchatShimmer();
                            });
                      } else {
                        if (snapshot.data?.length == 0) {
                          return const Center(child: Text('Aucune information'));
                        } else {
                          return ListView.builder(
                              shrinkWrap: true,
                              scrollDirection: Axis.vertical,
                              itemCount: snapshot.data?.length,
                              itemBuilder: (context, int index) {
                                return BuildItemAchat(snapshot: snapshot.data!.elementAt(index), context: context);
                              });
                        }
                      }
                    }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// get list mes achats
  Future<List<Achat>> get_mes_achats(String type) async {
    List<Achat> listachats = [];
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_achat.php?token=${token}&type=${type}'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      listachats = List<Achat>.from(l.map((model) => Achat.fromJson(model)));
    }
    return listachats;
  }
}
