import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/discussion_vendeur.dart';
import 'package:my_application/shimmer_effect/item_messagerie_vendeur_shimmer.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:my_application/utils/string_constant.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'Model/messagerie_vendeur_item.dart';

class MessagerieVendeur extends StatefulWidget {
  const MessagerieVendeur({super.key});

  @override
  State<MessagerieVendeur> createState() => _MessagerieVendeurState();
}

class _MessagerieVendeurState extends State<MessagerieVendeur> {
  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
  String text_lu = "Lu";
  String text_non_lu = "Non lu";

  bool count_detected_lu = true;

  bool count_detected_non_lu = true;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: DefaultTabController(
        length: 2,
        child: Scaffold(
          backgroundColor: ColorConstant.background_home_page,
          appBar: AppBar(
              surfaceTintColor: Colors.white,
              elevation: 3,
              shadowColor: Colors.black,
              backgroundColor: Colors.white,
              title: const Text('Messages'),
              bottom: TabBar(
                indicatorSize: TabBarIndicatorSize.tab,
                tabs: [
                  Tab(text: "${text_non_lu}"),
                  Tab(text: "${text_lu}"),
                ],
              )),
          body: Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: TabBarView(
              children: [
                FutureBuilder<List<MessagerieVendeurItem>>(
                    future: get_messagerie_vendeur("0"),
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) {
                        return ListView.builder(
                            shrinkWrap: true,
                            scrollDirection: Axis.vertical,
                            itemCount: 8,
                            itemBuilder: (context, int index) {
                              return const ItemMessagerieVendeurShimmer();
                            });
                      } else {
                        if (snapshot.data?.length == 0) {
                          return const Center(child: Text('Aucune information'));
                        } else {
                          return ListView.builder(
                              shrinkWrap: true,
                              scrollDirection: Axis.vertical,
                              itemCount: snapshot.data?.length,
                              itemBuilder: (context, int index) {
                                return BuildItemMessagerieVendeur(snapshot.data!.elementAt(index), context, "nonlu");
                              });
                        }
                      }
                    }),
                FutureBuilder<List<MessagerieVendeurItem>>(
                    future: get_messagerie_vendeur("1"),
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) {
                        return ListView.builder(
                            shrinkWrap: true,
                            scrollDirection: Axis.vertical,
                            itemCount: 8,
                            itemBuilder: (context, int index) {
                              return const ItemMessagerieVendeurShimmer();
                            });
                      } else {
                        if (snapshot.data?.length == 0) {
                          return const Center(child: Text('Aucune information'));
                        } else {
                          return ListView.builder(
                              shrinkWrap: true,
                              scrollDirection: Axis.vertical,
                              itemCount: snapshot.data?.length,
                              itemBuilder: (context, int index) {

                                return BuildItemMessagerieVendeur(snapshot.data!.elementAt(index), context, "lu");

                              });
                        }
                      }
                    }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  BuildItemMessagerieVendeur(MessagerieVendeurItem snapshot, context, String type) {
    String firstlettre = snapshot.nomUtil![0].toUpperCase();
    return GestureDetector(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Card(
            child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(mainAxisSize: MainAxisSize.max, children: [
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  border: Border.all(color: ColorConstant.red_enchere),
                  borderRadius: const BorderRadius.all(Radius.circular(30)),
                ),
                child: Center(
                    child: Text(
                  "${firstlettre}",
                  style: TextStyle(fontWeight: type == "nonlu" ? FontWeight.bold : FontWeight.normal),
                )),
              ),
            ),
            Expanded(
              child: Column(mainAxisSize: MainAxisSize.min, crossAxisAlignment: CrossAxisAlignment.start, children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text("${snapshot.nomAcheteur}", style: TextStyle(fontWeight: type == "nonlu" ? FontWeight.bold : FontWeight.normal)),
                    Text("${snapshot.dateDiscussion!.substring(0, (snapshot.dateDiscussion!.length) - 3)}",
                        style: TextStyle(fontWeight: type == "nonlu" ? FontWeight.bold : FontWeight.normal)),
                  ],
                ),
                Text("${snapshot.titreProduit}", style: TextStyle(fontWeight: type == "nonlu" ? FontWeight.bold : FontWeight.normal)),
              ]),
            )
          ]),
        )
        ),
      ),
      onTap: (){
        Navigator.push(context, MaterialPageRoute(builder: (context) =>  DiscussionVendeur(id_product: snapshot.idProduit!, title_product: snapshot.titreProduit!, nom_vendeur: snapshot.nomVendeur!, id_acheteur: snapshot.idAcheteur!, nom_acheteur: snapshot.nomAcheteur!, id_vendeur: snapshot.idVendeur!, id_discussion: snapshot.idDiscussion!)));
      },
    );
  }

  /// get list messagerie vendeur
  Future<List<MessagerieVendeurItem>> get_messagerie_vendeur(String etat) async {
    List<MessagerieVendeurItem> listMessagerieVendeurItem = [];
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_list_message_vendeur.php?token=${token}&etat=${etat}'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      listMessagerieVendeurItem = List<MessagerieVendeurItem>.from(l.map((model) => MessagerieVendeurItem.fromJson(model)));

      if (count_detected_non_lu == true) {
        if (etat == "0") {
          setState(() {
            text_non_lu = "Non lu (${listMessagerieVendeurItem.length})";
          });
          count_detected_non_lu = false;
        }
      }

      if (count_detected_lu == true) {
        if (etat == "1") {
          setState(() {
            text_lu = "Lu (${listMessagerieVendeurItem.length})";
          });
          count_detected_lu = false;
        }
      }
    }

    return listMessagerieVendeurItem;
  }
}
