class BoostDetail {
  String? produit;
  String? produitImg;
  String? boost;
  List<Planification>? planification;

  BoostDetail({this.produit, this.produitImg, this.boost, this.planification});

  BoostDetail.fromJson(Map<String, dynamic> json) {
    produit = json['produit'];
    produitImg = json['produit_img'];
    boost = json['boost'];
    if (json['planification'] != null) {
      planification = <Planification>[];
      json['planification'].forEach((v) {
        planification!.add(new Planification.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['produit'] = this.produit;
    data['produit_img'] = this.produitImg;
    data['boost'] = this.boost;
    if (this.planification != null) {
      data['planification'] =
          this.planification!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Planification {
  String? idCalendrier;
  String? idJetonBoost;
  String? utilisateurId;
  String? produitId;
  String? idcategorie;
  String? idemplacement;
  String? dateplanifie;
  String? execute;

  Planification(
      {this.idCalendrier,
        this.idJetonBoost,
        this.utilisateurId,
        this.produitId,
        this.idcategorie,
        this.idemplacement,
        this.dateplanifie,
        this.execute});

  Planification.fromJson(Map<String, dynamic> json) {
    idCalendrier = json['idCalendrier'];
    idJetonBoost = json['idJetonBoost'];
    utilisateurId = json['utilisateurId'];
    produitId = json['produitId'];
    idcategorie = json['idcategorie'];
    idemplacement = json['idemplacement'];
    dateplanifie = json['dateplanifie'];
    execute = json['execute'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['idCalendrier'] = this.idCalendrier;
    data['idJetonBoost'] = this.idJetonBoost;
    data['utilisateurId'] = this.utilisateurId;
    data['produitId'] = this.produitId;
    data['idcategorie'] = this.idcategorie;
    data['idemplacement'] = this.idemplacement;
    data['dateplanifie'] = this.dateplanifie;
    data['execute'] = this.execute;
    return data;
  }
}
