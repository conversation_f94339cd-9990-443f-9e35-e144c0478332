import 'dart:convert';

import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:get_storage/get_storage.dart';
import 'package:my_application/Model/motif_signal.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/utils/string_constant.dart';

import '../Model/store.dart';

class ListMotifSignalSharedprefrence extends GetxController{
  final GetStorage box = GetStorage();
  List<MotifSignal> Listmotif = [] ;
  ListMotifSignalSharedprefrence();

  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();

  List<MotifSignal> ListMotifSignalSharedprefrence_data(){
    try {
    if(box.hasData('motif_signal')) {
      Listmotif = List<MotifSignal>.from(box.read('motif_signal'));
      if (Listmotif.isNotEmpty) {
        return Listmotif ;
      } else {
        return [];
      }
    }else{
      return [];
    }
    } catch (e) {
      // No specified type, handles all
      print('Something really unknown: $e');
    }

    return [] ;
  }



  get_list_motif_signal() async{
      List<MotifSignal> list_motif_signal = [];
      box.write('motif_signal' , list_motif_signal) ;
      var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_list_signal_motif.php'));
        Iterable l = json.decode(res.body);
        List<MotifSignal> list_motif = List<MotifSignal>.from(l.map((model) => MotifSignal.fromJson(model)));
        list_motif.forEach((motif){
          list_motif_signal.add(motif);
        });
        box.write('motif_signal', list_motif_signal);

  }
}