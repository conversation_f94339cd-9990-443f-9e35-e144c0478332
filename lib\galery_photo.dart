import 'dart:convert';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:pinch_zoom_release_unzoom/pinch_zoom_release_unzoom.dart';


class GaleryPhoto extends StatefulWidget {
  var position;
  var list_photo;

  GaleryPhoto({required this.position, required this.list_photo, super.key});


  @override
  State<GaleryPhoto> createState() => _GaleryPhotoState();
}

class _GaleryPhotoState extends State<GaleryPhoto> {

  final items = [
    Image.network(
      'https://tunisie-enchere.com/media/offre/default_218x151.jpg',
      fit: BoxFit.cover,
    )
  ];

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top :16.0),
      child: Container(
        color: Colors.black54,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.center,
              child: SizedBox(
                height : MediaQuery.of(context).size.height ,
                width: MediaQuery.of(context).size.width,
                child: CarouselSlider(
                  options: CarouselOptions(
                    initialPage: widget.position,
                    viewportFraction: 1,
                    autoPlay: false,
                    enableInfiniteScroll: false,
                    aspectRatio: MediaQuery.of(context).size.width/MediaQuery.of(context).size.height,
                  ),
                  items: (widget.list_photo.length != 0) ? getListImage(widget.list_photo) : items,
                ),
              ),
            ),
            Positioned(
              right: 15,
                top: 15,
                child: GestureDetector(
                  child: Container(
                    decoration: BoxDecoration(
                      border : Border.all(color: Colors.white),
                      color: Colors.black,
                    ),
                    child: const Padding(
                      padding: EdgeInsets.only(left: 8.0 , right: 8.0 , top: 4.0 , bottom: 4.0),
                      child: Text("Fermer"  ,style: TextStyle(color: Colors.white, fontSize: 11 ,fontWeight: FontWeight.bold ,decoration: TextDecoration.none)),
                    ),
                  ),
                  onTap: () => Navigator.of(context).pop(),
                )
            )
          ],
        ),
      ),
    );
  }
  List<Widget> getListImage(List<String> snapshop) {
    List<Widget> imageCells = [];
    for (String snap in snapshop) {
      var cell = SizedBox(
        height : MediaQuery.of(context).size.height ,
        width: MediaQuery.of(context).size.width,
        child: PinchZoomReleaseUnzoomWidget(
          boundaryMargin: const EdgeInsets.only(bottom: 0),
          clipBehavior: Clip.none,
          fingersRequiredToPinch: 2,
          overlayColor: Colors.black,
          child: Image.network(snap,
                              errorBuilder: (BuildContext context, Object exception,StackTrace? stackTrace) {
                                return Image.network("https://tunisie-enchere.com/media/offre/default_218x151.jpg");
                              }),
        ),
      );
      imageCells.add(cell);
    }
    return imageCells;
  }
}

