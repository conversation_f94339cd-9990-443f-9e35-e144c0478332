import 'package:flutter/material.dart';
import 'package:my_application/utils/color_constant.dart';

import '../Model/ville.dart';

class SelectionVille extends StatefulWidget {
  const SelectionVille({super.key});

  @override
  State<SelectionVille> createState() => _SelectionVilleState();
}

class _SelectionVilleState extends State<SelectionVille> {
  late List<Ville> listvilleparent;
  late List<Ville> listvillechild;
  String villeparentselected = "0";
  String name_villeparent = "" ;

  @override
  void initState() {
    super.initState();
    listvillechild = [];
    listvilleparent = Ville.get_all_ville_parent();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        surfaceTintColor: Colors.white,
        elevation: 3,
        shadowColor: Colors.black,
        backgroundColor: Colors.white,
        automaticallyImplyLeading: true,
      ),
      body: Column(children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              height: 50,
              child: Center(
                  child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context,["0","Ville"]);
                },
                style: ElevatedButton.styleFrom(backgroundColor: ColorConstant.second_color),
                child: const Text("Toute la Tunisie", style: TextStyle(color: Colors.white)),
              )),
            ),
          ],
        ),
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ///// list parent ville
              Expanded(
                flex: 3,
                child: ListView.separated(
                  shrinkWrap: true,
                  padding: const EdgeInsets.only(bottom: 20,top: 10),
                  itemBuilder: (BuildContext context, int index) => GestureDetector(
                    child: Container(
                      height: 30,
                      padding: const EdgeInsets.all(0),
                      margin: const EdgeInsets.all(0),
                      decoration: BoxDecoration(
                          border: Border(
                        left: BorderSide(
                          color: listvilleparent.elementAt(index).id! == villeparentselected ? const Color(0xFFE4032E) : Colors.white,
                          width: 5,
                        ),
                      )),
                      child: villeparentselected == "0"
                          ? Center(child: Text(listvilleparent.elementAt(index).nom!))
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(left: 2.0),
                                  child: Text(listvilleparent.elementAt(index).nom!),
                                ),
                              ],
                            ),
                    ),
                    onTap: () {
                      listvillechild = Ville.get_all_ville_child(listvilleparent.elementAt(index).id!);
                      name_villeparent = listvilleparent.elementAt(index).nom!;
                      setState(() {
                        villeparentselected = listvilleparent.elementAt(index).id!;
                      });
                    },
                  ),
                  itemCount: listvilleparent.length,
                  separatorBuilder: (BuildContext context, int index) {
                    return const Divider(
                      color: Colors.black12,
                    );
                  },
                ),
              ),

              /////list child ville
              villeparentselected != "0"
                  ? Expanded(
                      flex: 7,
                      child: Container(
                        color: ColorConstant.background_home_page,
                        child: ListView.separated(
                          shrinkWrap: true,
                          padding: const EdgeInsets.only(bottom: 10,top: 10),
                          itemBuilder: (BuildContext context, int index) => GestureDetector(
                            child: SizedBox(
                                height: 30,
                                child: Container(
                                    height: 30,
                                    padding: const EdgeInsets.all(0),
                                    margin: const EdgeInsets.all(0),
                                    child: Center(child: Text(listvillechild.elementAt(index).nom!)))),
                            onTap: () {
                              if(index == 0 ){
                                Navigator.pop(context,[listvillechild.elementAt(index).id , name_villeparent]);
                              }else{
                                Navigator.pop(context,[listvillechild.elementAt(index).id , listvillechild.elementAt(index).nom]);
                              }

                            },
                          ),
                          itemCount: listvillechild.length,
                          separatorBuilder: (BuildContext context, int index) {
                            return const Divider(
                              color: Colors.black12,
                            );
                          },
                        ),
                      ),
                    )
                  : const SizedBox(),
            ],
          ),
        )
      ]),
    );
  }
}

