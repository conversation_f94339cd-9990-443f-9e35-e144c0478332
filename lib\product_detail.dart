import 'dart:async';
import 'dart:convert';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart' hide CarouselController;
import 'package:flutter/services.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/Model/item_historique.dart';
import 'package:my_application/store_detail.dart';
import 'package:my_application/utils/check_authenticate.dart';
import 'package:my_application/utils/list_motif_signal_sharedprefrence.dart';
import 'package:my_application/utils/wishlist_product_sharedprefrence.dart';
import 'package:my_application/widget/show_custom_loader_dialog.dart';
import 'package:share_plus/share_plus.dart';
import 'package:my_application/Model/detail_one_product.dart';
import 'package:my_application/galery_photo.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:my_application/utils/string_constant.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher_string.dart';

import 'Model/motif_signal.dart';
import 'Model/response_enchere.dart';
import 'discussion_client.dart';
import 'login.dart';

class ProductDetail extends StatefulWidget {
  ProductDetail({required this.idProduct, required this.nomProduct, super.key});

  var idProduct;
  var nomProduct;

  @override
  State<ProductDetail> createState() => _ProductDetailState();
}

class _ProductDetailState extends State<ProductDetail> {
  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
  final items = [
    Image.asset(
      'assets/images/logo.png',
      fit: BoxFit.cover,
    )
  ];

  int currentIndex = 0;
  final CarouselSliderController _carouselSliderController = CarouselSliderController();
  late Future<DetailOneProduct> myFutureProduct;
  late DetailOneProduct currentproduct;

  late DatabaseReference _counterRef;
  late DatabaseReference _messagesRef;
  late StreamSubscription<DatabaseEvent> _counterSubscription;
  late StreamSubscription<DatabaseEvent> _messagesSubscription;
  String _kTestKey = '0';
  String _kTestValue = '0';
  FirebaseException? _error;
  bool initialized = false;

  String prixproduct = "0.000";

  Timer? countdownTimer;
  late Duration myDuration;

  int _currentenchereIntValue = 0;

  int min_value_enchere = 0;

  List<ItemHistorique> list_historique_enchere = [];
  List<String> list_fav_product = [];
  bool isLikedProduct = false;

  @override
  void initState() {
    init();
    super.initState();
    list_fav_product = WishlistProductSharedprefrence().WishlistProductSharedprefrence_data();
    isLikedProduct = list_fav_product.contains(widget.idProduct);
    myFutureProduct = getProductDetail();
  }

  @override
  Widget build(BuildContext context) {
    var top = 0.0;
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: FutureBuilder<DetailOneProduct>(
          future: myFutureProduct,
          builder: (context, snapshot) {
            if (!snapshot.hasData) {
              return const Center(child: CircularProgressIndicator());
            } else {
              return Scaffold(
                  body: NestedScrollView(
                headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
                  return [
                    SliverAppBar(
                      flexibleSpace: LayoutBuilder(builder: (BuildContext context, BoxConstraints constraints) {
                        top = constraints.biggest.height;
                        return FlexibleSpaceBar(
                          title: AnimatedOpacity(
                              duration: const Duration(milliseconds: 300),
                              opacity: top == MediaQuery.of(context).padding.top + kToolbarHeight ? 1.0 : 0.0,
                              child: Container(
                                margin: const EdgeInsets.only(right: 70),
                                child: Text(
                                  "${snapshot.data?.titreProduit}",
                                  style: const TextStyle(fontSize: 12.0, color: Colors.black),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              )),
                          background: top == MediaQuery.of(context).padding.top + kToolbarHeight
                              ? SizedBox()
                              : carrousel_photos_product(context, snapshot.data!),
                          collapseMode: CollapseMode.parallax,
                        );
                      }),
                      actions: [
                        isLikedProduct == true
                            ? Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30),
                                  color: Colors.white,
                                ),
                                margin: const EdgeInsets.all(5.0),
                                child: IconButton(
                                  icon: const Icon(Icons.favorite_outlined),
                                  color: ColorConstant.second_color,
                                  onPressed: () {
                                    // remove from favoris
                                    CkeckAuthenticate().checkAuthenticate_user().then((value) {
                                      if (value == true) {
                                        setState(() {
                                          isLikedProduct = false;
                                          WishlistProductSharedprefrence().remove_from_WishlistProductSharedprefrence_data(widget.idProduct);
                                        });
                                      } else {
                                        Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                                      }
                                    });
                                  },
                                ),
                              )
                            : Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30),
                                  color: Colors.white,
                                ),
                                margin: const EdgeInsets.all(5.0),
                                child: IconButton(
                                  icon: Icon(Icons.favorite_border_outlined),
                                  color: ColorConstant.second_color,
                                  onPressed: () {
                                    // Add to favoris

                                    CkeckAuthenticate().checkAuthenticate_user().then((value) {
                                      if (value == true) {
                                        setState(() {
                                          isLikedProduct = true;
                                          WishlistProductSharedprefrence().add_to_WishlistProductSharedprefrence_data(widget.idProduct);
                                        });
                                      } else {
                                        Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                                      }
                                    });
                                  },
                                ),
                              ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(30),
                            color: Colors.white,
                          ),
                          margin: const EdgeInsets.all(5.0),
                          child: IconButton(
                            icon: Icon(Icons.share),
                            color: ColorConstant.second_color,
                            onPressed: () {
                              // Add your action here
                              Share.share(snapshot.data!.linkShare!, subject: widget.nomProduct);
                            },
                          ),
                        ),
                      ],
                      surfaceTintColor: Colors.white,
                      backgroundColor: Colors.white,
                      //automaticallyImplyLeading: true,
                      expandedHeight: 220,
                      floating: true,
                      snap: false,
                      pinned: true,
                      shadowColor: Colors.black,
                      leading: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(30),
                          color: Colors.white,
                        ),
                        margin: const EdgeInsets.all(5.0),
                        child: IconButton(
                            icon: const Icon(Icons.keyboard_backspace, color: Colors.black54),
                            onPressed: () {
                              Navigator.of(context).pop();
                            }),
                      ),
                    )
                  ];
                },
                body: Stack(
                  children: [
                    SingleChildScrollView(
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Flexible(
                                  child: RichText(
                                    text: TextSpan(
                                      text: "${snapshot.data?.titreProduit}",
                                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold,color: Colors.black),
                                    ),
                                    maxLines: 1,
                                    textAlign: TextAlign.start,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          snapshot.data?.directProduit == "0"
                              ? Column(
                                  children: [
                                    Padding(
                                        //// date fin enchere
                                        padding: const EdgeInsets.only(left: 12.0, right: 12.0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            const Text(
                                              "Fin de l\'enchère :",
                                              style: TextStyle(fontSize: 14),
                                            ),
                                            type_encheredisplay()
                                            /*Text(
                                            "${returnPrix("${snapshot.data?.dernierEnchereProduit == "0" ? snapshot.data?.dernierEnchereProduit : prixproduct}")} DT",
                                            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                          )*/
                                          ],
                                        )),
                                    Padding(
                                        //// enchere
                                        padding: const EdgeInsets.only(left: 12.0, right: 12.0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            const Text(
                                              "Offre minimale",
                                              style: TextStyle(fontSize: 14),
                                            ),
                                            Text(
                                              "${returnPrix("${snapshot.data?.dernierEnchereProduit != "0" ? snapshot.data?.dernierEnchereProduit : snapshot.data?.prixProduit}")} DT",
                                              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                            )
                                          ],
                                        )),
                                    snapshot.data?.offrePrixDirect != "0"
                                        ? Padding(
                                            padding: const EdgeInsets.only(left: 12.0, right: 12.0),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.max,
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                const Text(
                                                  "Prix d\'achat direct",
                                                  style: TextStyle(fontSize: 14),
                                                ),
                                                Text(
                                                  "${returnPrix("${snapshot.data?.offrePrixDirect}")} DT",
                                                  style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                                )
                                              ],
                                            ))
                                        : const SizedBox(),
                                  ],
                                )
                              : Padding(
                                  padding: const EdgeInsets.only(left: 12.0, right: 12.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Text(
                                        "Prix d'achat direct",
                                        style: TextStyle(fontSize: 14),
                                      ),
                                      Text(
                                        "${snapshot.data?.prixProduit} DT",
                                        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                      )
                                    ],
                                  ),
                                ),
                          Padding(
                            padding: const EdgeInsets.only(left: 12.0, right: 12.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  "Adresse",
                                  style: TextStyle(fontSize: 14),
                                ),
                                Row(
                                  children: [
                                    Icon(Icons.location_pin, color: ColorConstant.second_color,size: 16),
                                    Text(
                                      "${snapshot.data?.nomVille}",
                                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 12.0, right: 12.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  "Vendeur",
                                  style: TextStyle(fontSize: 14),
                                ),
                                snapshot.data?.packVendeur != "0"
                                ? GestureDetector(
                                  child: Text(
                                    "${snapshot.data?.nomVendeur}",
                                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                  ),
                                  onTap: (){
                                    Navigator.push(
                                        context, MaterialPageRoute(builder: (context) => StoreDetail(idStore: snapshot.data!.idUtilisateur!)));

                                  },
                                )
                                : Text(
                                  "${snapshot.data?.nomVendeur}",
                                  style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 12.0, right: 12.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  "Téléphone",
                                  style: TextStyle(fontSize: 14),
                                ),
                                Row(
                                  children: [
                                    Icon(Icons.call, color: ColorConstant.second_color,size: 16),
                                    GestureDetector(
                                      child: Text(
                                        "${snapshot.data?.telVendeur}",
                                        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                      ),
                                      onTap: (){
                                        launchUrlString("tel://${snapshot.data?.telVendeur}");
                                      },
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.only(top: 8, bottom: 2),
                            height: 10,
                            decoration: BoxDecoration(
                              color: ColorConstant.gray_background_detail_product,
                            ),
                          ),
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  "Description",
                                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                  textAlign: TextAlign.start,
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 15.0, right: 15),
                            child: GridView.builder(
                                gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                                    mainAxisExtent: 60, maxCrossAxisExtent: 250, crossAxisSpacing: 0, mainAxisSpacing: 0),
                                shrinkWrap: true,
                                physics: NeverScrollableScrollPhysics(),
                                itemCount: snapshot.data?.attribute?.length,
                                itemBuilder: (BuildContext ctx, index) {
                                  return attribue(snapshot.data!.attribute!
                                      .elementAt(index)); //Itemrandomproduct(snapshot: snapshot.data!.elementAt(index), context: context, full: 2);
                                }),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0, right: 8),
                            child: Html(
                              data: snapshot.data!.descriptionProduit!,
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.only(top: 2, bottom: 2),
                            height: 10,
                            decoration: BoxDecoration(
                              color: ColorConstant.gray_background_detail_product,
                            ),
                          ),
                          const Padding(
                            padding: EdgeInsets.all(8.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  "Livraison & Paiement",
                                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                  textAlign: TextAlign.start,
                                ),
                              ],
                            ),
                          ),
                          const Padding(
                            padding: EdgeInsets.only(left: 14.0, right: 14.0, top: 5.0, bottom: 0.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  "Livraison",
                                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                                  textAlign: TextAlign.start,
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: 14.0, right: 14.0, top: 5.0, bottom: 0.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                snapshot.data!.livraisonProduit! == "1"
                                    ? Text(
                                        "Retrait par l'acheteur à ${snapshot.data!.nomVille}",
                                        style: const TextStyle(fontSize: 14, color: Colors.black54),
                                        textAlign: TextAlign.start,
                                      )
                                    : const Text(
                                        "Livraison A Domicile",
                                        style: TextStyle(fontSize: 14, color: Colors.black54),
                                        textAlign: TextAlign.start,
                                      )
                              ],
                            ),
                          ),
                          const Padding(
                            padding: EdgeInsets.only(left: 14.0, right: 14.0, top: 8.0, bottom: 0.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  "Mode de Paiement",
                                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                                  textAlign: TextAlign.start,
                                ),
                              ],
                            ),
                          ),
                          const Padding(
                            padding: EdgeInsets.only(left: 14.0, right: 14.0, top: 5.0, bottom: 5.0),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  "Paiement au retrait de l'article",
                                  style: TextStyle(fontSize: 14, color: Colors.black54),
                                  textAlign: TextAlign.start,
                                )
                              ],
                            ),
                          ),
                          snapshot.data?.directProduit == "0"
                              ? Container(
                                  margin: const EdgeInsets.only(top: 2, bottom: 2),
                                  height: 10,
                                  decoration: BoxDecoration(
                                    color: ColorConstant.gray_background_detail_product,
                                  ),
                                )
                              : const SizedBox(),
                          snapshot.data?.directProduit == "0"
                              ? const Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Offres",
                                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                        textAlign: TextAlign.start,
                                      ),
                                    ],
                                  ),
                                )
                              : const SizedBox(),
                          snapshot.data?.directProduit == "0"
                              ? Padding(
                                  padding: EdgeInsets.only(left: 14.0, right: 14.0, top: 5.0, bottom: 5.0),
                                  child: list_historique_enchere.isNotEmpty
                                      ? ListView.builder(
                                          itemCount: list_historique_enchere.length,
                                          shrinkWrap: true,
                                          scrollDirection: Axis.vertical,
                                          itemBuilder: (context, index) {
                                            ItemHistorique item_historique = list_historique_enchere[index];
                                            return SizedBox(
                                              height: 60,
                                              width: MediaQuery.sizeOf(context).width,
                                              child: ListTile(
                                                title: Text("${item_historique.nomUtilUtilisateur}"),
                                                trailing: SizedBox(
                                                  width: (MediaQuery.sizeOf(context).width/10)*6 , // Set the appropriate width for your content
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.end,
                                                    children: [
                                                      Text(
                                                        "${item_historique.prixEnchere}",
                                                        style: const TextStyle(fontSize: 13, fontWeight: FontWeight.bold),
                                                      ),
                                                      Html(data: "${item_historique.dateEnchere}"),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            );
                                          },
                                        )
                                      : const Center(child: Text("Aucune enchère n\'a encore été placée sur cette offre")),
                                )
                              : const SizedBox(),
                          Container(
                            margin: const EdgeInsets.only(top: 2, bottom: 2),
                            height: 10,
                            decoration: BoxDecoration(
                              color: ColorConstant.gray_background_detail_product,
                            ),
                          ),
                          SizedBox(
                            height: 50,
                            child: Padding(
                              padding: EdgeInsets.only(left: 14.0, right: 14.0, top: 5.0, bottom: 5.0),
                              child: GestureDetector(
                                onTap: () {
                                  CkeckAuthenticate().checkAuthenticate_user().then((value) {
                                    if (value == true) {
                                      showDialogSignalerOffre();
                                    } else {
                                      Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                                    }
                                  });
                                },
                                child: const Row(
                                  mainAxisSize: MainAxisSize.max,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Icon(Icons.warning_rounded, size: 20, color: Colors.black54),
                                    SizedBox(width: 5),
                                    Text(
                                      "Signaler cette offre",
                                      style: TextStyle(fontSize: 14, color: Colors.black54),
                                      textAlign: TextAlign.start,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Container(
                            margin: const EdgeInsets.only(top: 2, bottom: 2),
                            height: 60,
                            decoration: BoxDecoration(
                              color: ColorConstant.gray_background_detail_product,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Positioned(
                      right: 10,
                      bottom: 60,
                      child: FloatingActionButton(
                        onPressed: () {
                          CkeckAuthenticate().checkAuthenticate_user().then((value) async {
                            if (value == true) {
                              String? id_user = await _prefs.then((data) => data.getString('id_user'));
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => DiscussionClient(
                                          id_product: currentproduct.id!,
                                          title_product: currentproduct.titreProduit!,
                                          nom_vendeur: currentproduct.nomVendeur!,
                                          id_user: id_user!,
                                          id_vendeur: currentproduct.idUtilisateur!)));
                            } else {
                              Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                            }
                          });
                        },
                        foregroundColor: Colors.white,
                        backgroundColor: ColorConstant.red_enchere,
                        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(50.0))),
                        child: const Icon(Icons.comment_rounded),
                      ),
                    ),
                    currentproduct.directProduit == "0" // enchere
                        ? Positioned(
                            right: 0,
                            left: 0,
                            bottom: 0,
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                Expanded(
                                  child: GestureDetector(
                                    child: Container(
                                      height: 50,
                                      decoration: BoxDecoration(
                                        color: ColorConstant.second_color,
                                      ),
                                      child: const Center(
                                          child: Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(Icons.hardware_rounded, color: Colors.white),
                                          Padding(
                                              padding: EdgeInsets.only(left: 8.0), child: Text('Enchérir', style: TextStyle(color: Colors.white))),
                                        ],
                                      )),
                                    ),
                                    onTap: () {
                                      CkeckAuthenticate().checkAuthenticate_user().then((value) {
                                        if (value == true) {
                                          showDialogEncherir();
                                        } else {
                                          Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                                        }
                                      });
                                    },
                                  ),
                                ),
                                currentproduct.offrePrixDirect != "0"
                                    ? Expanded(
                                        child: GestureDetector(
                                          child: Container(
                                            height: 50,
                                            decoration: BoxDecoration(
                                              color: ColorConstant.green_enchere,
                                            ),
                                            child: const Center(
                                                child: Row(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              children: [
                                                Icon(Icons.monetization_on_outlined, color: Colors.white),
                                                Padding(
                                                    padding: EdgeInsets.only(left: 8.0),
                                                    child: Text('Achat direct', style: TextStyle(color: Colors.white))),
                                              ],
                                            )),
                                          ),
                                          onTap: () {
                                            CkeckAuthenticate().checkAuthenticate_user().then((value) {
                                              if (value == true) {
                                                showDialogAchatDirect();
                                              } else {
                                                Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                                              }
                                            });
                                          },
                                        ),
                                      )
                                    : const SizedBox()
                              ],
                            ),
                          )
                        : currentproduct.propositionPrixProduit == "1"
                            ? Positioned(
                                right: 0,
                                left: 0,
                                bottom: 0,
                                child: GestureDetector(
                                  child: Container(
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: ColorConstant.green_enchere,
                                    ),
                                    child: const Center(
                                        child: Row(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Icon(Icons.monetization_on_outlined, color: Colors.white),
                                        Padding(
                                            padding: EdgeInsets.only(left: 8.0),
                                            child: Text('Proposer un prix', style: TextStyle(color: Colors.white))),
                                      ],
                                    )),
                                  ),
                                  onTap: () {
                                    CkeckAuthenticate().checkAuthenticate_user().then((value) {
                                      if (value == true) {
                                        showDialogProposerPrix();
                                      } else {
                                        Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                                      }
                                    });
                                  },
                                ),
                              )
                            : const SizedBox()
                  ],
                ),
              ));
            }
          }),
    );
  }

  Future<DetailOneProduct> getProductDetail() async {
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_info_product.php?id=${widget.idProduct}'));
    if (res.statusCode == 200) {
      currentproduct = DetailOneProduct.fromJson(json.decode(res.body));

      if (currentproduct.directProduit == "0") {
        if (currentproduct.dernierEnchereProduit == "0") {
          min_value_enchere = int.parse(currentproduct.prixProduit!.replaceAll(' ', ''));
          _currentenchereIntValue = int.parse(currentproduct.prixProduit!.replaceAll(' ', ''));
        } else {
          min_value_enchere =
              int.parse(currentproduct.dernierEnchereProduit!.replaceAll(' ', '')) + get_increment_offre(currentproduct.incrementProduit!);
          _currentenchereIntValue =
              int.parse(currentproduct.dernierEnchereProduit!.replaceAll(' ', '')) + get_increment_offre(currentproduct.incrementProduit!);
        }
      }

      DateTime now = new DateTime.now();
      myDuration = DateTime.parse(currentproduct.finDateProduit!).difference(now);
      startTimer();
      get_list_enchere_offre("${currentproduct.id}");

      return DetailOneProduct.fromJson(json.decode(res.body));
    } else {
      return DetailOneProduct();
    }
  }

  Widget carrousel_photos_product(context, DetailOneProduct data) {
    return Column(
      children: [
        Stack(
          alignment: AlignmentDirectional.bottomCenter,
          children: [
            CarouselSlider(
              carouselController: _carouselSliderController,
              options: CarouselOptions(
                viewportFraction: 1,
                height: 210,
                autoPlay: false,
                enableInfiniteScroll: false,
                onPageChanged: (index, reason) {
                  setState(() {
                    currentIndex = index;
                  });
                },
              ),
              items: (data.linksImage?.length != 0) ? getListImage(data.linksImage!) : items,
            ),
            DotsIndicator(
                dotsCount: (data.linksImage?.length != 0) ? data.linksImage!.length : 1,
                decorator: const DotsDecorator(
                  spacing: EdgeInsets.all(4.0),
                  activeColor: Color(0xFFE4032E),
                ),
                position: currentIndex,
                onTap: (position) {
                  _carouselSliderController.animateToPage(position);
                })
          ],
        ),
      ],
    );
  }

  List<Widget> getListImage(List<String> snapshop) {
    List<Widget> imageCells = [];
    for (String snap in snapshop) {
      var cell = GestureDetector(
        child: Container(
          height: 250,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: NetworkImage(snap),
              //AssetImage('assets/images/travel_2.png') ,
              fit: BoxFit.cover,
              alignment: Alignment.center,
              onError:(error, stackTrace) => NetworkImage('https://tunisie-enchere.com/media/offre/default_218x151.jpg'),
            ),
          ),
        ),
        onTap: () =>
            {Navigator.push(context, MaterialPageRoute(builder: (context) => GaleryPhoto(position: snapshop.indexOf(snap), list_photo: snapshop)))},
      );
      imageCells.add(cell);
    }
    return imageCells;
  }

  Widget attribue(Attribute attribute) {
    return SizedBox(
      height: 50,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("${attribute.attributeName}", style: const TextStyle(fontWeight: FontWeight.bold)),
          Text("${attribute.attributeValue}"),
        ],
      ),
    );
  }

  String returnPrix(String prix) {
    return prix;
    /*if (prix.length <= 3) {
      return prix;
    } else if (prix.length > 3 && prix.length <= 6) {
      return '${prix.substring(0, prix.length - 3)} ${prix.substring(prix.length - 3)}';
    } else {
      return '${prix.substring(0, prix.length - 6)} ${prix.substring(prix.length - 6, prix.length - 3)} ${prix.substring(prix.length - 3)}';
    }*/
  }

  showDialogSignalerOffre() {
    List<MotifSignal> list_motif_signal = ListMotifSignalSharedprefrence().ListMotifSignalSharedprefrence_data();
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
                shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
                contentPadding: const EdgeInsets.only(top: 10.0),
                title: SizedBox(
                  height: 50,
                  child: Stack(
                    children: [
                      const Center(
                        heightFactor: 50,
                        child: Text(
                          'Signaler cette offre !',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: IconButton(
                            padding: const EdgeInsets.all(0),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            icon: const Icon(
                              Icons.close,
                              color: Colors.black38,
                              size: 18,
                            )),
                      )
                    ],
                  ),
                ),
                content: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [Expanded(child: Center(child: Text(currentproduct.titreProduit!)))]),
                      SizedBox(
                        width: double.maxFinite,
                        height: 60 * list_motif_signal.length * 1.0,
                        child: ListView.separated(
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: list_motif_signal.length,
                          itemBuilder: (BuildContext context, int index) {
                            return ListTile(
                              title: GestureDetector(
                                child: SizedBox(
                                    height: 40,
                                    child: Center(
                                        child: Text(
                                      list_motif_signal[index].text!,
                                      style: TextStyle(fontSize: 12),
                                    ))),
                                onTap: () {
                                  action_signal_offre("${list_motif_signal[index].id}", "${currentproduct.id}");
                                },
                              ),
                            );
                          },
                          separatorBuilder: (context, index) {
                            return const Divider(height: 1);
                          },
                        ),
                      ),
                      const Center(
                          child: Text(
                        "Aidez-nous à comprendre le probléme avec cet offre. Comment la décririez-vous ?",
                        style: TextStyle(fontSize: 11),
                      )),
                      const SizedBox(height: 5),
                    ],
                  ),
                ));
          });
        });
  }

  /// firebase usage
  Future<void> init() async {
    _counterRef = FirebaseDatabase.instance.ref('produit').child('${widget.idProduct}').child('prix');

    final database = FirebaseDatabase.instance;

    database.setLoggingEnabled(false);

    if (!kIsWeb) {
      database.setPersistenceEnabled(true);
      database.setPersistenceCacheSizeBytes(10000000);
    }

    if (!kIsWeb) {
      await _counterRef.keepSynced(true);
    }

    setState(() {
      initialized = true;
    });
    _counterSubscription = _counterRef.onValue.listen(
      (DatabaseEvent event) {
        setState(() {
          _error = null;
          prixproduct = event.snapshot.value as String;
          get_list_enchere_offre('${widget.idProduct}');
        });
      },
      onError: (Object o) {
        final error = o as FirebaseException;
        setState(() {
          _error = error;
        });
      },
    );
  }

  action_signal_offre(String type_motif, String id_product) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    String _url = '${StringConstant.base_url}api/action_signal_offre.php';
    var res = await http.post(Uri.parse(_url), headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }, body: {
      'token': token,
      'idprod': id_product,
      'typesignal': type_motif,
    });
    Navigator.of(context).pop();
    try {
      ResponseEnchere _response_enchere = ResponseEnchere.fromJson(json.decode(res.body));
      Fluttertoast.showToast(
          msg: "${_response_enchere.message}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.green,
          textColor: Colors.white,
          fontSize: 16.0);
    } catch (e) {
      print('Something really unknown: $e');
    }
  }

  Widget type_encheredisplay() {
    String timer_enchere = "";

    String strDigits(int n) => n.toString().padLeft(2, '0');
    final days = strDigits(myDuration.inDays);
    final hours = strDigits(myDuration.inHours.remainder(24));
    final minutes = strDigits(myDuration.inMinutes.remainder(60));
    final seconds = strDigits(myDuration.inSeconds.remainder(60));

    if (days == "00") {
      if (hours == "00") {
        if (minutes == "00") {
          if (seconds == "00") {
            timer_enchere = "Offre Terminée";
          } else {
            timer_enchere = "Reste ${int.parse(seconds)} sec";
          }
        } else {
          timer_enchere = "Reste ${int.parse(minutes)}min ${int.parse(seconds)}sec ";
        }
      } else {
        if (minutes == "00") {
          timer_enchere = "Reste ${int.parse(hours)}h";
        } else {
          timer_enchere = "Reste ${int.parse(hours)}h ${int.parse(minutes)}min ";
        }
      }
    } else {
      if (hours == "00") {
        timer_enchere = "Reste ${int.parse(days)}j";
      } else {
        timer_enchere = "Reste ${int.parse(days)}j ${int.parse(hours)}h ";
      }
    }

    if (int.parse(seconds) < 0) {
      timer_enchere = "Offre Terminée";
    }

    return Text("$timer_enchere", style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16));
  }

  void startTimer() {
    if (mounted) {
      countdownTimer = Timer.periodic(Duration(seconds: 1), (_) => setCountDown());
    }
  }

  void setCountDown() {
    if (mounted) {
      final reduceSecondsBy = 1;
      setState(() {
        final seconds = myDuration.inSeconds - reduceSecondsBy;
        if (seconds < 0) {
          countdownTimer!.cancel();
        } else {
          myDuration = Duration(seconds: seconds);
        }
      });
    }
  }

  void stopTimer() {
    setState(() => countdownTimer!.cancel());
  }

  showDialogEncherir() {
    int increment_offre = get_increment_offre(currentproduct.incrementProduit!);

    if (currentproduct.dernierEnchereProduit == "0") {
      min_value_enchere = int.parse(currentproduct.prixProduit!.replaceAll(' ', ''));
      _currentenchereIntValue = int.parse(currentproduct.prixProduit!.replaceAll(' ', ''));
    } else {
      min_value_enchere =
          int.parse(currentproduct.dernierEnchereProduit!.replaceAll(' ', '')) + get_increment_offre(currentproduct.incrementProduit!);
      if (prixproduct == "0.000") {
        _currentenchereIntValue =
            int.parse(currentproduct.dernierEnchereProduit!.replaceAll(' ', '')) + get_increment_offre(currentproduct.incrementProduit!);
      } else {
        _currentenchereIntValue = int.parse(prixproduct.replaceAll(' ', '').split('.')[0]) + get_increment_offre(currentproduct.incrementProduit!);
      }
    }

    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Enchérir',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.all(0),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              shape: BoxShape.rectangle,
                              border: Border.all(
                                color: Colors.black,
                                width: 1.0,
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.only(left: 8.0, right: 5),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  IconButton(
                                    icon: Icon(Icons.remove),
                                    onPressed: () => setState(() {
                                      final newValue = _currentenchereIntValue - increment_offre;
                                      if (newValue >= min_value_enchere) {
                                        _currentenchereIntValue = newValue;
                                      }
                                    }),
                                  ),
                                  Text('$_currentenchereIntValue'),
                                  IconButton(
                                    icon: Icon(Icons.add),
                                    onPressed: () => setState(() {
                                      final newValue = _currentenchereIntValue + increment_offre;
                                      _currentenchereIntValue = newValue;
                                    }),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    )
                  ],
                ),
              ),
              actions: [
                Row(
                  children: [
                    Expanded(
                      flex: 5,
                      child: InkWell(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 4, top: 4, right: 2, bottom: 2),
                          child: Container(
                              height: 40,
                              decoration: BoxDecoration(color: ColorConstant.second_color, borderRadius: BorderRadius.circular(7.0)),
                              child: const Center(
                                  child: Text("Fermer", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                        ),
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                    Expanded(
                      flex: 5,
                      child: InkWell(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 2, top: 4, right: 4, bottom: 2),
                            child: Container(
                                height: 40,
                                decoration: BoxDecoration(color: ColorConstant.red_enchere, borderRadius: BorderRadius.circular(7.0)),
                                child: const Center(
                                    child: Text("Enchérir", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                          ),
                          onTap: () {
                            Navigator.of(context).pop();
                            Alert_dialog_confirm_enchere(_currentenchereIntValue);
                          }),
                    ),
                  ],
                )
              ],
            );
          });
        });
  }

  Alert_dialog_confirm_enchere(int val_enchere) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Confirmation enchérir',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.all(0),
                            child: const Padding(
                              padding: EdgeInsets.only(left: 8.0, right: 5),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text('Etes-vous sûr d\'enchérir?'),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    )
                  ],
                ),
              ),
              actions: [
                Row(
                  children: [
                    Expanded(
                      flex: 5,
                      child: InkWell(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 4, top: 4, right: 2, bottom: 2),
                          child: Container(
                              height: 40,
                              decoration: BoxDecoration(color: ColorConstant.second_color, borderRadius: BorderRadius.circular(7.0)),
                              child: const Center(
                                  child: Text("Non", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                        ),
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                    Expanded(
                      flex: 5,
                      child: InkWell(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 2, top: 4, right: 4, bottom: 2),
                            child: Container(
                                height: 40,
                                decoration: BoxDecoration(color: ColorConstant.red_enchere, borderRadius: BorderRadius.circular(7.0)),
                                child: const Center(
                                    child: Text("Oui", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                          ),
                          onTap: () {
                            Navigator.of(context).pop();
                            action_add_enchere(currentproduct.id!, val_enchere);
                          }),
                    ),
                  ],
                )
              ],
            );
          });
        });
  }

  showDialogAchatDirect() {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Confirmation achat direct',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.all(0),
                            child: const Padding(
                              padding: EdgeInsets.only(left: 8.0, right: 5),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text('Etes-vous sûr d\'acheter le produit ?'),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    )
                  ],
                ),
              ),
              actions: [
                Row(
                  children: [
                    Expanded(
                      flex: 5,
                      child: InkWell(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 4, top: 4, right: 2, bottom: 2),
                          child: Container(
                              height: 40,
                              decoration: BoxDecoration(color: ColorConstant.second_color, borderRadius: BorderRadius.circular(7.0)),
                              child: const Center(
                                  child: Text("Non", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                        ),
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                    Expanded(
                      flex: 5,
                      child: InkWell(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 2, top: 4, right: 4, bottom: 2),
                            child: Container(
                                height: 40,
                                decoration: BoxDecoration(color: ColorConstant.red_enchere, borderRadius: BorderRadius.circular(7.0)),
                                child: const Center(
                                    child: Text("Oui", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                          ),
                          onTap: () {
                            Navigator.of(context).pop();
                            action_achat_direct(currentproduct.id!);
                          }),
                    ),
                  ],
                )
              ],
            );
          });
        });
  }

  showDialogProposerPrix() {
    TextEditingController proposition_controller = TextEditingController();

    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Proposer un prix',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Row(
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Expanded(
                          flex: 10,
                          child: Padding(
                            padding: EdgeInsets.only(left: 8.0, right: 5),
                            child: Text('Le vendeur répondra à votre proposition dans un délais maximum de 48h', maxLines: 2),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            margin: const EdgeInsets.all(0),
                            child: const Padding(
                              padding: EdgeInsets.only(left: 8.0, right: 5),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('Proposition :'),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Container(
                      height: 50,
                      padding: const EdgeInsets.only(left: 10, right: 10),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: proposition_controller,
                              inputFormatters: [FilteringTextInputFormatter.allow(RegExp('[0-9.,]+'))],
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(
                                    borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(30.0),
                                        bottomLeft: Radius.circular(30.0),
                                        bottomRight: Radius.circular(0),
                                        topRight: Radius.circular(0))),
                                fillColor: Colors.white,
                                filled: true,
                              ),
                              autofocus: false,
                              cursorColor: Colors.black,
                              keyboardType: TextInputType.number,
                              textInputAction: TextInputAction.next,
                              onChanged: (value) {},
                            ),
                          ),
                          Container(
                              width: 50,
                              height: 50,
                              decoration: BoxDecoration(
                                  color: ColorConstant.second_color,
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(0),
                                      bottomLeft: Radius.circular(0),
                                      topRight: Radius.circular(30.0),
                                      bottomRight: Radius.circular(30.0))),
                              child: const Center(
                                  child: Text("TND", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 10,
                    )
                  ],
                ),
              ),
              actions: [
                Row(
                  children: [
                    Expanded(
                      flex: 5,
                      child: InkWell(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 4, top: 4, right: 2, bottom: 2),
                          child: Container(
                              height: 40,
                              decoration: BoxDecoration(color: ColorConstant.second_color, borderRadius: BorderRadius.circular(7.0)),
                              child: const Center(
                                  child: Text("Fermer", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                        ),
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                    Expanded(
                      flex: 5,
                      child: InkWell(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 2, top: 4, right: 4, bottom: 2),
                            child: Container(
                                height: 40,
                                decoration: BoxDecoration(color: ColorConstant.red_enchere, borderRadius: BorderRadius.circular(7.0)),
                                child: const Center(
                                    child: Text("Proposer", style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.normal)))),
                          ),
                          onTap: () {
                            Navigator.of(context).pop();
                            add_proposition_prix("${proposition_controller.text}");
                          }),
                    ),
                  ],
                )
              ],
            );
          });
        });
  }

  action_add_enchere(String id_prod, int val_enchere) async {
    ShowCustomLoaderDialog().showCustomDialog(context);
    String? token = await _prefs.then((data) => data.getString('user_token'));
    String _url = '${StringConstant.base_url}api/add_enchere.php';
    var res = await http.post(Uri.parse(_url), headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }, body: {
      'token': token,
      'id_Produit': id_prod,
      'valeur_offre': "$val_enchere",
    });

    ResponseEnchere _response_enchere = ResponseEnchere.fromJson(json.decode(res.body));
    Navigator.of(context).pop();
    if (_response_enchere.etat == "0") {
      Fluttertoast.showToast(
          msg: "${_response_enchere.message}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0);
    } else if (_response_enchere.etat == "1") {
      Fluttertoast.showToast(
          msg: "${_response_enchere.message}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.green,
          textColor: Colors.white,
          fontSize: 16.0);
    } else if (_response_enchere.etat == "3") {
      Fluttertoast.showToast(
          msg: "${_response_enchere.message}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0);
    } else if (_response_enchere.etat == "4") {
      action_achat_direct(id_prod);
    }
  }

  action_achat_direct(String id_prod) async {
    ShowCustomLoaderDialog().showCustomDialog(context);
    String? token = await _prefs.then((data) => data.getString('user_token'));
    String _url = '${StringConstant.base_url}api/add_achat_direct.php';
    var res = await http.post(Uri.parse(_url), headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }, body: {
      'token': token,
      'id_Produit': id_prod,
    });

    ResponseEnchere _response_enchere = ResponseEnchere.fromJson(json.decode(res.body));
    Navigator.of(context).pop();
    if (_response_enchere.etat == "0") {
      Fluttertoast.showToast(
          msg: "${_response_enchere.message}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0);
    } else if (_response_enchere.etat == "1") {
      Fluttertoast.showToast(
          msg: "${_response_enchere.message}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.green,
          textColor: Colors.white,
          fontSize: 16.0);
    } else if (_response_enchere.etat == "3") {
      Fluttertoast.showToast(
          msg: "${_response_enchere.message}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0);
    }
  }

  add_proposition_prix(String prix_proposer) async {

    ShowCustomLoaderDialog().showCustomDialog(context);
    String? token = await _prefs.then((data) => data.getString('user_token'));
    String? id_user = await _prefs.then((data) => data.getString('id_user'));
    print(id_user);
    print(currentproduct.id);
    print(currentproduct.idUtilisateur);
    print(prix_proposer);
    String _url = '${StringConstant.base_url}api/add_proposition_prix.php';
    var res = await http.post(Uri.parse(_url), headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }, body: {
      'token': token,
      'id_produit': "${currentproduct.id}",
      'id_vendeur': "${currentproduct.idUtilisateur}",
      'id_acheteur': "${id_user}",
      'type_user': "0",
      'message_content': "${prix_proposer}",
    });

    ResponseEnchere _response_enchere = ResponseEnchere.fromJson(json.decode(res.body));
    Navigator.of(context).pop();
    if (_response_enchere.etat == "0") {
      Fluttertoast.showToast(
          msg: "${_response_enchere.message}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.red,
          textColor: Colors.white,
          fontSize: 16.0);
    } else if (_response_enchere.etat == "1") {
      Fluttertoast.showToast(
          msg: "${_response_enchere.message}",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: Colors.green,
          textColor: Colors.white,
          fontSize: 16.0);
    }
  }

  int get_increment_offre(String pas) {
    switch (pas) {
      case "1":
        return 1;
      case "2":
        return 5;
      case "3":
        return 10;
      case "4":
        return 20;
      case "5":
        return 50;
      case "6":
        return 100;
      default:
        return 1;
    }
  }

  void get_list_enchere_offre(String id_product) async {
    List<ItemHistorique> historique_enchere_product = [];
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_list_enchere_offre.php?id=${id_product}'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      List<ItemHistorique> listhistorique = List<ItemHistorique>.from(l.map((model) => ItemHistorique.fromJson(model)));
      historique_enchere_product = listhistorique;
    }
    setState(() {
      list_historique_enchere = historique_enchere_product;
    });
  }
}
