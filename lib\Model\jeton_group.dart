

class JetonGroup {
  String? id;
  String? titre;
  String? qte;
  String? prix;

  JetonGroup({this.id, this.titre, this.qte, this.prix});

  JetonGroup.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    titre = json['titre'];
    qte = json['qte'];
    prix = json['prix'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['titre'] = this.titre;
    data['qte'] = this.qte;
    data['prix'] = this.prix;
    return data;
  }
}
