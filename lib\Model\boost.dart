import 'jeton_group.dart';
import 'jeton_operations.dart';

class Boost {
  String? id;
  String? soldeToken;
  List<JetonOperations>? jetonOperations;
  List<JetonGroup>? jetonGroup;

  Boost({this.id, this.soldeToken, this.jetonOperations, this.jetonGroup});

  Boost.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    soldeToken = json['solde_token'];
    if (json['jeton_operations'] != null) {
      jetonOperations = <JetonOperations>[];
      json['jeton_operations'].forEach((v) {
        jetonOperations!.add(new JetonOperations.fromJson(v));
      });
    }
    if (json['jeton_group'] != null) {
      jetonGroup = <JetonGroup>[];
      json['jeton_group'].forEach((v) {
        jetonGroup!.add(new JetonGroup.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['solde_token'] = this.soldeToken;
    if (this.jetonOperations != null) {
      data['jeton_operations'] =
          this.jetonOperations!.map((v) => v.toJson()).toList();
    }
    if (this.jetonGroup != null) {
      data['jeton_group'] = this.jetonGroup!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}