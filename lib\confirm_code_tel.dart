import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:my_application/inscription.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/utils/string_constant.dart';
import 'Model/response_inscription.dart';

class ConfirmCodeTel extends StatefulWidget {

  String user_tel = "" ;
  ConfirmCodeTel(this.user_tel,{super.key});

  @override
  State<ConfirmCodeTel> createState() => _ConfirmCodeTelState();
}

class _ConfirmCodeTelState extends State<ConfirmCodeTel> {

  late String _code_tel = "";
  bool _visibleerror = false;
  String errortext = "";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
              IconButton(onPressed: () => Navigator.of(context).pop(), icon: Icon(Icons.keyboard_backspace, color: ColorConstant.second_color))
            ]),
          ),
          const SizedBox(height: 20),
          Row(mainAxisAlignment: MainAxisAlignment.center, children: [
            Image.asset(
              'assets/images/logo.png',
              height: 40,
            )
          ]),
          const SizedBox(height: 30),
          const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [Text("Bienvenue!", style: TextStyle(color: Colors.black, fontSize: 14, decoration: TextDecoration.none))]),
          const SizedBox(height: 5),
          Row(mainAxisAlignment: MainAxisAlignment.center, mainAxisSize: MainAxisSize.max, children: [
            SizedBox(
              width: MediaQuery.of(context).size.width,
              child: const Padding(
                  padding: EdgeInsets.only(right: 30, left: 30),
                  child: Text("Remplissez le formulaire pour commencer",
                      style: TextStyle(color: Colors.black45, fontSize: 12, decoration: TextDecoration.none),
                      softWrap: true,
                      textAlign: TextAlign.center)),
            )
          ]),
          const SizedBox(height: 10),
          SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Padding(
                padding: const EdgeInsets.only(right: 30, left: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        color: ColorConstant.gray_background_detail_product,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8.0, left: 8.0,top: 5.0,bottom: 5.0),
                        child: Text("Un code a été envoyer au ${widget.user_tel}",
                            style: const TextStyle(color: Colors.black45, fontSize: 12, decoration: TextDecoration.none),
                            softWrap: true,
                            textAlign: TextAlign.center),
                      ),
                    ),
                  ],
                )),
          ),
          const SizedBox(height: 20),
          Row(mainAxisAlignment: MainAxisAlignment.center, children: [
            SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Padding(
                  padding: const EdgeInsets.only(right: 30, left: 30),
                  child: TextFormField(
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.black),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.black),
                      ),
                      fillColor: Colors.white,
                      labelText: "Code de vérification",
                      labelStyle: TextStyle(fontSize: 14, color: Colors.black),
                      prefixIcon: Padding(
                        padding: EdgeInsets.only(right: 8.0, left: 8.0),
                        child: Icon(
                          Icons.message ,
                          color: Colors.black,
                        ),
                      ),
                      filled: true,
                    ),
                    autofocus: false,
                    cursorColor: Colors.black,
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.done,
                    onChanged: (value) => _code_tel = value,
                  ),
                ))
          ]),
          const SizedBox(height: 10),
          _visibleerror == true
              ? SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Padding(
              padding: const EdgeInsets.only(left: 30.0, right: 30.0, top: 10.0, bottom: 10.0),
              child: Text(errortext,
                  style: const TextStyle(color: Colors.red, fontSize: 12, decoration: TextDecoration.none),
                  softWrap: true,
                  textAlign: TextAlign.start),
            ),
          )
              : const SizedBox(height: 20),
          Row(mainAxisAlignment: MainAxisAlignment.center, children: [
            SizedBox(
              height: 50,
              width: MediaQuery.of(context).size.width,
              child: Padding(
                padding: const EdgeInsets.only(right: 30, left: 30),
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorConstant.red_enchere,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  onPressed: () {
                    confirm_code() ;

                  },
                  child:const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Center(child: Text("Je confirme mon numéro", style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 13))),
                    Icon(Icons.check, color: Colors.white),
                  ],
                ),
                ),
              ),
            )
          ]),
          const SizedBox(height: 20),
        ]),
      ),
    );
  }

  void confirm_code() async {
    try {

      var res = await http.post(Uri.parse('${StringConstant.base_url}templates/verif_inscri.php'), headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      }, body: {
        'phone': widget.user_tel,
        'code': _code_tel,
        'pass': "",
        'cgu_confirm': "",
        'email': "",
        'villeInscrit': "",
        'nom': "",
        'step': "code",
        'token_fcm': "",
      });

      if (res.statusCode == 200) {
        ResponseInscription responseinscription = ResponseInscription.fromJson(jsonDecode(res.body));
        if (responseinscription != null) {
          if (responseinscription.error == "1") {
            setState(() {
              errortext = responseinscription.message!;
              _visibleerror = true;
            });
          } else {
            ///////////////////////////////////////
            //// go to Inscription //////////////////
            ///////////////////////////////////////
            Navigator.push(context, MaterialPageRoute(builder: (context) => Inscription(widget.user_tel,"$_code_tel")));
          }
        } else {
          setState(() {
            errortext = "Une erreur s'est produite, contactez le support";
            _visibleerror = true;
          });
        }
      } else {
        setState(() {
          errortext = "Une erreur s'est produite, contactez le support";
          _visibleerror = true;
        });
      }
    } on Exception catch (e) {
      print(e);
      throw Exception("Error on server");
    }
  }
}
