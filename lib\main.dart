import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:app_links/app_links.dart';
import 'package:convex_bottom_bar/convex_bottom_bar.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get_storage/get_storage.dart';
import 'package:my_application/messagerie_vendeur.dart';
import 'package:my_application/product_detail.dart';
import 'package:my_application/product_search.dart';
import 'package:my_application/profil_detail.dart';
import 'package:my_application/store_detail.dart';
import 'package:my_application/utils/list_motif_signal_sharedprefrence.dart';
import 'package:my_application/widget/item_home_mes_annonce.dart';
import 'package:my_application/wishlist_product_list.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:my_application/product_list.dart';
import 'package:my_application/shimmer_effect/build_categorie_item_shimmer.dart';
import 'package:my_application/shimmer_effect/item_product_shimmer.dart';
import 'package:my_application/shimmer_effect/item_random_product_shimmer.dart';
import 'package:my_application/store_list.dart';
import 'package:my_application/utils/check_authenticate.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/utils/string_constant.dart';
import 'package:my_application/utils/style_bottom_nav.dart';
import 'package:my_application/widget/build_categorie_item.dart';
import 'package:my_application/widget/itemrandomproduct.dart';
import 'package:my_application/wishlist_store_list.dart';
import 'Model/Categorie.dart';
import 'Model/product.dart';
import 'add_product.dart';
import 'discussion_vendeur.dart';
import 'jetons.dart';
import 'login.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import 'mes_achats.dart';
import 'mes_ventes.dart';

//const kWindowsScheme = 'https';

void main() async {
  await GetStorage.init();
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  final FirebaseMessaging messaging = FirebaseMessaging.instance;

  // Request permissions for iOS
  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    badge: true,
    sound: true,
  );

  if (settings.authorizationStatus == AuthorizationStatus.authorized) {
    print("Notification permission granted.");
  } else if (settings.authorizationStatus ==
      AuthorizationStatus.provisional) {
    print("Provisional permission granted.");
  } else {
    print("Notification permission denied.");
  }

//  registerProtocolHandler(kWindowsScheme);

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    FlutterNativeSplash.remove();

    return GetMaterialApp(
      locale: Get.deviceLocale,
      debugShowCheckedModeBanner: false,
      title: 'Tunisie enchère',
      theme: ThemeData(
          fontFamily: 'Araboto',
          canvasColor: Colors.transparent,
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.white),
          useMaterial3: true,
          appBarTheme: const AppBarTheme(
            systemOverlayStyle: SystemUiOverlayStyle(
              statusBarColor: Colors.white,
              statusBarIconBrightness: Brightness.dark,
              statusBarBrightness: Brightness.light,
            ),
          )),
      localizationsDelegates: const [GlobalMaterialLocalizations.delegate],
      supportedLocales: const [Locale('fr', 'FR')],
      home: const SafeArea(
          minimum: EdgeInsets.only(top: 16.0),
          child:
              MyHomePage()), //ProductDetail(idProduct: "1049", nomProduct: "LAND ROVER RANGE ROVER EVOQUE KIT HAMANN") , // ProductDetail(idProduct: "1675", nomProduct: "Robe de mariée") , //roductList( "","",order: "1") ,//const SelectionVille() , //const MyHomePage(),// const StoreDetail(idStore : "466" , nomUtilStore : "test seif" ) ,
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  Completer<void> _refreshCompleter = Completer<void>();
  CkeckAuthenticate ckeckAuthenticate = Get.put(CkeckAuthenticate());

  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();

  final GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey<ScaffoldState>();
  late Future<List<Categorie>> myFuturelistCategorie;
  late Future<Product?> myFutureRandomProduct;
  late Future<List<Product>> myFuturelistProducts;
  late Future<List<Product>> myFuturelistProducts_alaune;
  late Future<List<Product>> myFuturelistProducts_mes_annonce;
  late Future<List<Product>> myFuturelistDernierMinute;
  late bool islogged = false;
  late AppLinks _appLinks;
  late bool ala_une = false;
  late bool dernier_minute = false;

  late bool mes_annances = false;

  StreamSubscription<Uri>? _linkSubscription;

  late FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin;

  // It is assumed that all messages contain a data field with the key 'type'
  Future<void> setupInteractedMessage() async {
    RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      _handleMessage(initialMessage);
    }
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);
  }

  void _handleMessage(RemoteMessage message) {
    if (message.data["type"] == "1") {
      // product
      Navigator.push(context,
          MaterialPageRoute(builder: (context) => ProductDetail(idProduct: message.data["id_product"], nomProduct: message.data["nom_product"])));
    } else if (message.data["type"] == "2") {
      // discussion
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => DiscussionVendeur(
                  id_product: message.data["id_product"],
                  title_product: message.data["title_product"],
                  nom_vendeur: message.data["nom_vendeur"],
                  id_acheteur: message.data["id_acheteur"],
                  nom_acheteur: message.data["nom_acheteur"],
                  id_vendeur: message.data["id_vendeur"],
                  id_discussion: message.data["id_discussion"])));
    } else if (message.data["type"] == "3") {
      // store
      Navigator.push(context, MaterialPageRoute(builder: (context) => StoreDetail(idStore: message.data["id_store"])));
    } else if (message.data["type"] == "4") {
      // categorie
      Navigator.push(context, MaterialPageRoute(builder: (context) => ProductList("", message.data["id_categorie"], order: "1")));
    }
  }

  Future<void> _showNotification(String id, String title, String body, Map<String, dynamic> data) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics = AndroidNotificationDetails(
      '1',
      'tun_ench',
      channelDescription: 'local notification',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: false,
    );
    const NotificationDetails platformChannelSpecifics = NotificationDetails(android: androidPlatformChannelSpecifics);
    await flutterLocalNotificationsPlugin.show(
      0,
      title,
      body,
      platformChannelSpecifics,
      payload: json.encode(data),
    );
  }

  @override
  void initState() {
    /*requestPermissionMedia() ;
    requestPermissionPhoto() ;
    requestPermissionMediaLibrary() ;
    requestPermissionCamera() ;
    requestPermissionStorage() ;
    requestPermissionLocalStorage() ;*/

    call_permession();

    send_fcm();

    CkeckAuthenticate().checkAuthenticate_user().then((value) => islogged = value);
    super.initState();
    myFuturelistCategorie = getListCategorie();
    myFutureRandomProduct = getRandomProduct();
    myFuturelistProducts = getlistProduct();
    myFuturelistProducts_alaune = getlistProductAlaUne();
    myFuturelistProducts_mes_annonce = getlistProductMesAnnone();
    myFuturelistDernierMinute = getlistProductDernierMinute();
    ListMotifSignalSharedprefrence().get_list_motif_signal();

    // setup action click on notification in background
    setupInteractedMessage();

    FirebaseMessaging.instance.requestPermission();
    FirebaseMessaging.instance.subscribeToTopic('broadcast_tunisie_enchere');

    /**
     * code local notification
     */
    flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    const AndroidInitializationSettings initializationSettingsAndroid = AndroidInitializationSettings('@mipmap/ic_launcher');

    final DarwinInitializationSettings initializationSettingsIOS =
        const DarwinInitializationSettings(requestSoundPermission: false, requestBadgePermission: false, requestAlertPermission: false);

    final InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onSelectNotification,
    );
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      // generate local notification when recive notification when application open
      if (message.notification != null) {
        _showNotification(message.messageId!, message.notification!.title!, message.notification!.body!, message.data);
      }
    });

    /**
     * when click on link
     */
    initDeepLinks();
  }

  /**
   * action when click on local notification
   */
  Future<void> _onSelectNotification(NotificationResponse response) async {
    Map<String, dynamic> messagedata = jsonDecode(response.payload!);
    if (messagedata["type"] == "1") {
      // product
      Navigator.push(context,
          MaterialPageRoute(builder: (context) => ProductDetail(idProduct: messagedata["id_product"], nomProduct: messagedata["nom_product"])));
    } else if (messagedata["type"] == "2") {
      // discussion
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => DiscussionVendeur(
                  id_product: messagedata["id_product"],
                  title_product: messagedata["title_product"],
                  nom_vendeur: messagedata["nom_vendeur"],
                  id_acheteur: messagedata["id_acheteur"],
                  nom_acheteur: messagedata["nom_acheteur"],
                  id_vendeur: messagedata["id_vendeur"],
                  id_discussion: messagedata["id_discussion"])));
    } else if (messagedata["type"] == "3") {
      // store
      Navigator.push(context, MaterialPageRoute(builder: (context) => StoreDetail(idStore: messagedata["id_store"])));
    } else if (messagedata["type"] == "4") {
      // categorie
      Navigator.push(context, MaterialPageRoute(builder: (context) => ProductList("", messagedata["id_categorie"], order: "1")));
    }
  }

  Future<void> initDeepLinks() async {
    _appLinks = AppLinks();
    // Handle links
    _linkSubscription = _appLinks.uriLinkStream.listen((uri) {
      debugPrint('onAppLink: $uri');
      String deep_link_product = uri.toString();
      if (deep_link_product != "") {
        String uri_product = deep_link_product;
        if (uri_product.toString().contains("/a/")) {
          String pathUrlProduct = uri_product.toString();
          int lastPosition = pathUrlProduct.lastIndexOf("/");
          int firstPosition = pathUrlProduct.substring(0, lastPosition).lastIndexOf("/");
          String idProduct = pathUrlProduct.substring(firstPosition + 1, lastPosition);
          deep_link_product = "";
          Navigator.push(context, MaterialPageRoute(builder: (context) => ProductDetail(idProduct: idProduct, nomProduct: '')));
        }
      }
    });
  }

  @override
  void dispose() {
    _linkSubscription?.cancel();
    super.dispose();
  }

  /*
  Future<void> requestPermissionLocalStorage() async {
    final permissionmanageExternalStorage = Permission.manageExternalStorage;
    if (await permissionmanageExternalStorage.isDenied) {
      await permissionmanageExternalStorage.request();
    }
  }
  Future<void> requestPermissionStorage() async {
    final permissionmStorage = Permission.storage;
    if (await permissionmStorage.isDenied) {
      await permissionmStorage.request();
    }
  }*/
  call_permession() async {
    await CkeckPermission();
  }

  Future<void> CkeckPermission() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.camera,
      Permission.phone,
      Permission.storage,
      Permission.notification
    ].request();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: StyleProvider(
        style: StyleBottomNav(),
        child: ConvexAppBar(
          curveSize: 80,
          height: 55,
          style: TabStyle.fixedCircle,
          // if you are using fixed circle type, the TabItem must be odd number
          backgroundColor: ColorConstant.background_home_page,
          color: ColorConstant.second_color,
          activeColor: ColorConstant.second_color,
          items: [
            TabItem(
                icon: Image(image: AssetImage('assets/images/accueil.png'), height: 15, color: ColorConstant.second_color),
                title: 'Accueil',
                fontFamily: 'arabotonormal'),
            TabItem(
                icon: Image(image: AssetImage('assets/images/messages.png'), height: 15, color: ColorConstant.second_color),
                title: 'Chat',
                fontFamily: 'arabotonormal'),
            TabItem(icon: Icons.add_outlined),
            TabItem(
                icon: Image(image: AssetImage('assets/images/profil.png'), height: 15, color: ColorConstant.second_color),
                title: 'Profil',
                fontFamily: 'arabotonormal'),
            TabItem(
                icon: Image(image: AssetImage('assets/images/favoris_accueil.png'), height: 15, color: ColorConstant.second_color),
                title: 'Favoris',
                fontFamily: 'arabotonormal'),
          ],
          //initialActiveIndex: 2,//optional, default as 0
          onTap: (int i) {
            //_tapNav(i);
            if (i == 0) {
              /*CkeckAuthenticate().checkAuthenticate_user().then((value) {
                if (value == true) {
                  setState(() {
                    islogged = value;
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => MesVentes()));
                } else {
                  setState(() {
                    islogged = value;
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                }
              });*/

              setState(() {
                myFuturelistCategorie = getListCategorie();
                myFutureRandomProduct = getRandomProduct();
                myFuturelistProducts = getlistProduct();
                myFuturelistProducts_alaune = getlistProductAlaUne();
                myFuturelistProducts_mes_annonce = getlistProductMesAnnone();
                myFuturelistDernierMinute = getlistProductDernierMinute();
              });
            } else if (i == 1) {
              /*CkeckAuthenticate().checkAuthenticate_user().then((value) {
                if (value == true) {
                  setState(() {
                    islogged = value;
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => MesAchats()));
                } else {
                  setState(() {
                    islogged = value;
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                }
              });*/

              CkeckAuthenticate().checkAuthenticate_user().then((value) {
                if (value == true) {
                  setState(() {
                    islogged = value;
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => const MessagerieVendeur()));
                } else {
                  setState(() {
                    islogged = value;
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                }
              });

            } else if (i == 3) {
              CkeckAuthenticate().checkAuthenticate_user().then((value) {
                if (value == true) {
                  setState(() {
                    islogged = value;
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => const ProfilDetail()));
                } else {
                  setState(() {
                    islogged = value;
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                }
              });
            } else if (i == 4) {
              CkeckAuthenticate().checkAuthenticate_user().then((value) {
                if (value == true) {
                  setState(() {
                    islogged = value;
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => const WishlistProductList()));
                } else {
                  setState(() {
                    islogged = value;
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                }
              });
            } else if (i == 2) {
              CkeckAuthenticate().checkAuthenticate_user().then((value) {
                if (value == true) {
                  setState(() {
                    islogged = value;
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => AddProduct(id_offre: "")));
                } else {
                  setState(() {
                    islogged = value;
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                }
              });
            }
          },
        ),
      ),
      backgroundColor: ColorConstant.background_home_page,
      key: _scaffoldKey,
      drawer: Drawer(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(topRight: Radius.circular(0), bottomRight: Radius.circular(0)),
        ),
        child: ListView(
          // Important: Remove any padding from the ListView.
          padding: EdgeInsets.zero,
          children: [
            ListTile(
              tileColor: ColorConstant.second_color,
              titleTextStyle: const TextStyle(color: Colors.white),
              leading: const Image(image: AssetImage('assets/images/accueil.png'), height: 24, color: Colors.white),
              title: const Text('Accueil'),
              onTap: () {
                _scaffoldKey.currentState?.openEndDrawer();
              },
            ),
            ListTile(
              titleTextStyle: TextStyle(color: ColorConstant.second_color),
              leading: Image(image: AssetImage('assets/images/produit.png'), height: 24, color: ColorConstant.second_color),
              title: const Text('Produits'),
              onTap: () {
                _scaffoldKey.currentState?.openEndDrawer();
                Navigator.push(context, MaterialPageRoute(builder: (context) => ProductList("", "", order: "1")));
              },
            ),
            ListTile(
              titleTextStyle: TextStyle(color: ColorConstant.second_color),
              leading: Image(image: AssetImage('assets/images/boutique.png'), height: 24, color: ColorConstant.second_color),
              title: const Text('Boutiques'),
              onTap: () {
                _scaffoldKey.currentState?.openEndDrawer();
                Navigator.push(context, MaterialPageRoute(builder: (context) => const StoreList())).whenComplete(() {
                  CkeckAuthenticate().checkAuthenticate_user().then((value) {
                    islogged = value;
                  });
                });
              },
            ),
            ListTile(
              titleTextStyle: TextStyle(color: ColorConstant.second_color),
              leading: Image(image: AssetImage('assets/images/achat.png'), height: 24, color: ColorConstant.second_color),
              title: const Text('Mes achats'),
              onTap: () {
                _scaffoldKey.currentState?.openEndDrawer();
                CkeckAuthenticate().checkAuthenticate_user().then((value) {
                  if (value == true) {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => MesAchats()));
                  } else {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                  }
                });
              },
            ),
            ListTile(
              titleTextStyle: TextStyle(color: ColorConstant.second_color),
              leading: Image(image: AssetImage('assets/images/growth1.png'), height: 24, color: ColorConstant.second_color),
              title: const Text('Mes ventes'),
              onTap: () {
                _scaffoldKey.currentState?.openEndDrawer();
                CkeckAuthenticate().checkAuthenticate_user().then((value) {
                  if (value == true) {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => MesVentes()));
                  } else {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                  }
                });
              },
            ),
            ListTile(
              titleTextStyle: TextStyle(color: ColorConstant.second_color),
              leading: Image(image: AssetImage('assets/images/abonnements.png'), height: 24, color: ColorConstant.second_color),
              title: const Text('Boutiques favoris'),
              onTap: () {
                _scaffoldKey.currentState?.openEndDrawer();

                CkeckAuthenticate().checkAuthenticate_user().then((value) {
                  if (value == true) {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => const WishlistStoreList()));
                  } else {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                  }
                });
              },
            ),
            ListTile(
              titleTextStyle: TextStyle(color: ColorConstant.second_color),
              leading: Image(image: AssetImage('assets/images/favoris_accueil.png'), height: 24, color: ColorConstant.second_color),
              title: const Text('Favoris'),
              onTap: () {
                _scaffoldKey.currentState?.openEndDrawer();
                CkeckAuthenticate().checkAuthenticate_user().then((value) {
                  if (value == true) {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => const WishlistProductList()));
                  } else {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                  }
                });
              },
            ),
            ListTile(
              titleTextStyle: TextStyle(color: ColorConstant.second_color),
              leading: Image(image: AssetImage('assets/images/profil.png'), height: 24, color: ColorConstant.second_color),
              title: const Text('Profil'),
              onTap: () {
                _scaffoldKey.currentState?.openEndDrawer();
                CkeckAuthenticate().checkAuthenticate_user().then((value) {
                  if (value == true) {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => const ProfilDetail()));
                  } else {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                  }
                });
              },
            ),
            ListTile(
              titleTextStyle: TextStyle(color: ColorConstant.second_color),
              leading: Image(image: AssetImage('assets/images/messages.png'), height: 24, color: ColorConstant.second_color),
              title: const Text('Messages'),
              onTap: () {
                _scaffoldKey.currentState?.openEndDrawer();
                CkeckAuthenticate().checkAuthenticate_user().then((value) {
                  if (value == true) {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => const MessagerieVendeur()));
                  } else {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                  }
                });
              },
            ),
            ListTile(
              titleTextStyle: TextStyle(color: ColorConstant.second_color),
              leading: Image(image: AssetImage('assets/images/diamant.png'), height: 24, color: ColorConstant.second_color),
              title: const Text('Bi3 Asra3'),
              onTap: () {
                _scaffoldKey.currentState?.openEndDrawer();
                CkeckAuthenticate().checkAuthenticate_user().then((value) {
                  if (value == true) {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => const Jetons()));
                  } else {
                    setState(() {
                      islogged = value;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                  }
                });
              },
            ),
          ],
        ),
      ),
      body: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return [
            SliverAppBar(
              surfaceTintColor: Colors.white,
              backgroundColor: Colors.white,
              leading: IconButton(
                icon: const Icon(Icons.menu),
                onPressed: () => _scaffoldKey.currentState?.openDrawer(),
              ),
              title: Container(
                  color: Colors.white,
                  child: const Center(
                      child: Image(
                    image: AssetImage('assets/images/logo.png'),
                    height: 25,
                  ))),
              actions: [
                Padding(
                  padding: const EdgeInsets.only(right: 7.0),
                  child: InkWell(
                    child: Image(image: AssetImage('assets/images/diamant.png'), height: 20, color: ColorConstant.second_color),
                    onTap: () {
                      _scaffoldKey.currentState?.openEndDrawer();
                      CkeckAuthenticate().checkAuthenticate_user().then((value) {
                        if (value == true) {
                          setState(() {
                            islogged = value;
                          });
                          Navigator.push(context, MaterialPageRoute(builder: (context) => const Jetons()));
                        } else {
                          setState(() {
                            islogged = value;
                          });
                          Navigator.push(context, MaterialPageRoute(builder: (context) => Login()));
                        }
                      });
                    },
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.search),
                  onPressed: () {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => const ProductSearch()));
                  },
                ),
              ],
              forceElevated: true,
              elevation: 30.0,
              automaticallyImplyLeading: false,
              expandedHeight: 50,
              floating: true,
              snap: true,
              pinned: false,
              shadowColor: Colors.black,
            )
          ];
        },
        body: RefreshIndicator(
          backgroundColor: Colors.white,
          onRefresh: () async {
            await _pullRefresh();
            // await Future.delayed(const Duration(seconds: 2));
          },
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                const SizedBox(height: 10),
                SizedBox(
                  height: 100,
                  child: FutureBuilder<List<Categorie>>(
                      future: myFuturelistCategorie,
                      builder: (context, snapshot) {
                        if (!snapshot.hasData) {
                          return ListView.builder(
                              shrinkWrap: true,
                              scrollDirection: Axis.horizontal,
                              itemCount: 8,
                              itemBuilder: (context, int index) {
                                return const BuildCategorieItemShimmer();
                              });
                        } else {
                          return ListView.builder(
                              shrinkWrap: true,
                              scrollDirection: Axis.horizontal,
                              itemCount: snapshot.data?.length,
                              itemBuilder: (context, int index) {
                                return BuildCategorieItem(snapshot.data!.elementAt(index), context);
                              });
                        }
                      }),
                ),
                mes_annances
                    ? Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Text("Mes annonces",
                                  style: TextStyle(color: ColorConstant.second_color, fontWeight: FontWeight.bold), textAlign: TextAlign.start),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox(),
                mes_annances
                    ? SizedBox(
                        height: 280,
                        child: FutureBuilder<List<Product>>(
                            future: myFuturelistProducts_mes_annonce,
                            builder: (context, snapshot) {
                              if (!snapshot.hasData) {
                                return const SizedBox();
                              } else {
                                return ListView.builder(
                                    shrinkWrap: true,
                                    scrollDirection: Axis.horizontal,
                                    itemCount: snapshot.data?.length,
                                    itemBuilder: (context, int index) {
                                      return ItemHomeMesAnnonce(snapshot: snapshot.data!.elementAt(index), context: context, full: 2);
                                    });
                              }
                            }),
                      )
                    : const SizedBox(),
                ala_une
                    ? Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Text("À la une",
                                  style: TextStyle(color: ColorConstant.second_color, fontWeight: FontWeight.bold), textAlign: TextAlign.start),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox(),
                ala_une
                    ? SizedBox(
                        height: 260,
                        child: FutureBuilder<List<Product>>(
                            future: myFuturelistProducts_alaune,
                            builder: (context, snapshot) {
                              if (!snapshot.hasData) {
                                return const SizedBox();
                              } else {
                                return ListView.builder(
                                    shrinkWrap: true,
                                    scrollDirection: Axis.horizontal,
                                    itemCount: snapshot.data?.length,
                                    itemBuilder: (context, int index) {
                                      return Itemrandomproduct(snapshot: snapshot.data!.elementAt(index), context: context, full: 2);
                                    });
                              }
                            }),
                      )
                    : const SizedBox(),
                dernier_minute
                    ? Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text("Offre", style: TextStyle(color: ColorConstant.second_color), textAlign: TextAlign.start),
                          ),
                        ],
                      )
                    : SizedBox(),
                dernier_minute
                    ? Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Text("Dernière minute",
                                  style: TextStyle(color: ColorConstant.second_color, fontWeight: FontWeight.bold), textAlign: TextAlign.start),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: GestureDetector(
                                child: Text("Voir plus", style: TextStyle(color: ColorConstant.second_color), textAlign: TextAlign.start),
                                onTap: () {
                                  Navigator.push(context, MaterialPageRoute(builder: (context) => ProductList("", "", order: "2")));
                                },
                              ),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox(),
                dernier_minute
                    ? FutureBuilder<List<Product>>(
                        future: myFuturelistDernierMinute,
                        builder: (context, snapshot) {
                          if (!snapshot.hasData) {
                            return GridView.builder(
                                gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                                    mainAxisExtent: 250, maxCrossAxisExtent: 250, crossAxisSpacing: 0, mainAxisSpacing: 0),
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: 12,
                                itemBuilder: (BuildContext ctx, index) {
                                  return const ItemProductShimmer();
                                });
                          } else {
                            return GridView.builder(
                                gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                                    mainAxisExtent: 280, maxCrossAxisExtent: 250, crossAxisSpacing: 0, mainAxisSpacing: 0),
                                shrinkWrap: true,
                                physics: NeverScrollableScrollPhysics(),
                                itemCount: snapshot.data?.length,
                                itemBuilder: (BuildContext ctx, index) {
                                  return Itemrandomproduct(snapshot: snapshot.data!.elementAt(index), context: context, full: 2);
                                });
                          }
                        })
                    : const SizedBox(),
                dernier_minute
                    ? Container(
                        margin: EdgeInsets.all(10),
                        height: 40,
                        width: 200,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorConstant.second_color,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(15),
                            ),
                          ),
                          onPressed: () {
                            Navigator.push(context, MaterialPageRoute(builder: (context) => ProductList("", "", order: "2")));
                          },
                          child: Center(child: Text('VOIR PLUS'.toUpperCase(), style: const TextStyle(color: Color(0xFFFFFFFF), fontSize: 13))),
                        ),
                      )
                    : SizedBox(),
                Row(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text("Offre", style: TextStyle(color: ColorConstant.second_color), textAlign: TextAlign.start),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Text("Nouveaux",
                            style: TextStyle(color: ColorConstant.second_color, fontWeight: FontWeight.bold), textAlign: TextAlign.start),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: GestureDetector(
                          child: Text("Voir plus", style: TextStyle(color: ColorConstant.second_color), textAlign: TextAlign.start),
                          onTap: () {
                            Navigator.push(context, MaterialPageRoute(builder: (context) => ProductList("", "", order: "1")));
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                FutureBuilder<List<Product>>(
                    future: myFuturelistProducts,
                    builder: (context, snapshot) {
                      if (!snapshot.hasData) {
                        return GridView.builder(
                            gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                                mainAxisExtent: 250, maxCrossAxisExtent: 250, crossAxisSpacing: 0, mainAxisSpacing: 0),
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: 12,
                            itemBuilder: (BuildContext ctx, index) {
                              return const ItemProductShimmer();
                            });
                      } else {
                        return GridView.builder(
                            gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                                mainAxisExtent: 280, maxCrossAxisExtent: 250, crossAxisSpacing: 0, mainAxisSpacing: 0),
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: snapshot.data?.length,
                            itemBuilder: (BuildContext ctx, index) {
                              return Itemrandomproduct(snapshot: snapshot.data!.elementAt(index), context: context, full: 2);
                            });
                      }
                    }),
                Container(
                  margin: EdgeInsets.all(10),
                  height: 40,
                  width: 200,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorConstant.second_color,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                    onPressed: () {
                      Navigator.push(context, MaterialPageRoute(builder: (context) => ProductList("", "", order: "1")));
                    },
                    child: Center(child: Text('VOIR PLUS'.toUpperCase(), style: const TextStyle(color: Color(0xFFFFFFFF), fontSize: 13))),
                  ),
                ),
                const SizedBox(height: 25)
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _pullRefresh() async {
    _refreshCompleter = Completer<void>();
    setState(() {
      myFuturelistCategorie = getListCategorie();
      myFutureRandomProduct = getRandomProduct();
      myFuturelistProducts = getlistProduct();
      myFuturelistProducts_alaune = getlistProductAlaUne();
      myFuturelistProducts_mes_annonce = getlistProductMesAnnone();
      myFuturelistDernierMinute = getlistProductDernierMinute();
    });
    await Future.delayed(const Duration(milliseconds: 1200), () {
      _refreshCompleter.complete();
    });
  }

  Future<List<Categorie>> getListCategorie() async {
    List<Categorie> listcategorie = [];
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_list_categorie.php'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      List<Categorie> listcategorie = List<Categorie>.from(l.map((model) => Categorie.fromJson(model)));
      return listcategorie;
    } else {
      return listcategorie;
    }
  }

  Future<Product?> getRandomProduct() async {
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_random_gold_product.php'));
    if (res.statusCode == 200) {
      return Product.fromJson(json.decode(res.body));
    } else {
      return null;
    }
  }

  Future<List<Product>> getlistProduct() async {
    List<Product> listproduct = [];
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_list_product.php'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      List<Product> listproduct = List<Product>.from(l.map((model) => Product.fromJson(model)));
      return listproduct;
    } else {
      return listproduct;
    }
  }

  Future<List<Product>> getlistProductDernierMinute() async {
    List<Product> listproduct = [];
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_list_product.php?orderby=1'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      List<Product> listproduct = List<Product>.from(l.map((model) => Product.fromJson(model)));
      if (listproduct.isNotEmpty) {
        setState(() {
          dernier_minute = true;
        });
      }else{
        setState(() {
          dernier_minute = false;
        });
      }

      return listproduct;
    } else {
      setState(() {
        dernier_minute = false;
      });
      return listproduct;
    }
  }

  Future<List<Product>> getlistProductAlaUne() async {
    List<Product> listproduct = [];
    ala_une = false;
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_boost_a_la_une.php'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      List<Product> listproduct = List<Product>.from(l.map((model) => Product.fromJson(model)));
      if (listproduct.isNotEmpty) {
        setState(() {
          ala_une = true;
        });
      }
      return listproduct;
    } else {
      setState(() {
        ala_une = false;
      });
      return listproduct;
    }
  }

  Future<List<Product>> getlistProductMesAnnone() async {
    List<Product> listproduct = [];
    try {
      String? token = await _prefs.then((data) => data.getString('user_token'));
      mes_annances = false;
      if (token!.isNotEmpty) {
        var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_mes_annonces.php?token=${token}'));
        if (res.statusCode == 200) {
          Iterable l = json.decode(res.body);
          List<Product> listproduct = List<Product>.from(l.map((model) => Product.fromJson(model)));
          if (listproduct.isNotEmpty) {
            setState(() {
              mes_annances = true;
            });
          }
          return listproduct;
        } else {
          setState(() {
            mes_annances = false;
          });
          return listproduct;
        }
      } else {
        return listproduct;
      }
    } catch (e) {
      return listproduct;
    }
  }

  send_fcm() async {
    try {
      FirebaseMessaging.instance.getToken().then((_fcm_token) async {
        int is_ios = 1;
        if (Platform.isAndroid) {
          is_ios = 1;
        } else if (Platform.isIOS) {
          is_ios = 2;
        }
        await http.get(Uri.parse('${StringConstant.base_url}api/add_fcm_token.php?token=${_fcm_token}&is_ios=${is_ios}'));
      });
    } catch (e) {}
  }
}
