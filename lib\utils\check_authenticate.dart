import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CkeckAuthenticate extends GetxController{
  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
  RxBool loggedin = false.obs;
  CkeckAuthenticate(){}

  Future<bool> checkAuthenticate_user() async {
    String? user_token = await _prefs.then((data) => data.getString('user_token'));
    if (user_token != null && user_token.isNotEmpty) {
      loggedin = true.obs ;
      update();
      refresh();
      return true ;
    } else {
      loggedin = false.obs ;
      update();
      refresh();
      return false ;
    }
  }
}