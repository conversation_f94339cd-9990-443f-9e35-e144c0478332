import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:flutter_xlider/flutter_xlider.dart';
import 'package:intl/intl.dart';

import 'package:flutter/material.dart';
import 'package:flutter_tags/flutter_tags.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/Model/data_attribute.dart';
import 'package:my_application/Model/prix_and_categorie.dart';
import 'package:my_application/shimmer_effect/item_product_shimmer.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:my_application/utils/hexa_color.dart';
import 'package:my_application/utils/string_constant.dart';
import 'package:my_application/widget/itemrandomproduct.dart';
import 'package:my_application/widget/selection_ville.dart';

import 'Model/Categorie.dart';
import 'Model/product.dart';
import 'Model/type_attribute.dart';
import 'Model/ville.dart';

class ProductList extends StatefulWidget {
  ProductList(this.text_search, this.id_categorie, {required this.order, super.key});

  final String order;
  String text_search = "";
  String id_categorie = "";

  @override
  State<ProductList> createState() => _ProductListState();
}

class _ProductListState extends State<ProductList> {
  final GlobalKey<ScaffoldState> _scaffoldKey = new GlobalKey<ScaffoldState>();

  late List<Product> ListProduct;
  bool more_item = true;
  bool loader_bottom = false;
  String idcategorie = "";

  String id_ville_selected = "0";

  String ville_name = "";
  Ville ville_principale_selected = Ville();

  String titleorderby = "Nouveaux";

  String orderby = "1";

  int prix_min = 1;

  int prix_max = 0;

  var _controllerTextSearch = TextEditingController();
  List<Ville> villelist = Ville.get_all_ville_parent();
  List<Ville> villelist_child = [];

  Ville ville_selected = Ville(id: "0", nom: "--Ville--", idParent: "0");
  Ville ville_child_selected = Ville(id: "0", nom: "--Gouvernorate--", idParent: "0");

  bool ischecked_1 = false;

  bool ischecked_2 = false;

  bool ischecked_3 = false;

  bool ischecked_4 = false;

  bool ischecked_5 = false;

  bool ischecked_6 = false;

  Future<List<Categorie>> postcategorie = getListCategorie("0");

  List<Categorie> postpathcategorie = initpathcategorie();

  List<double> new_currentRangeValues = [0.0, 999.0];

  double Maxrange = 999;
  double Minrange = 0;
  int id_type_produit = 0;
  String id_color = "0";
  List<TypeAttribute> attributelist = [];
  int date_counter = 0;

  List<TextEditingController> listtextdate = [];
  TypeAttribute sousattributelist = TypeAttribute();

  List<String> list_attribute = [];
  List<String> list_id_attribute = [];

  String text_search = "";

  var initdataattr = null;

  bool existinit = false;

  List<Categorie> front_postpathcategorie = initfront_path_categorie();

  Map<String, List<double>> list_range_slider = {};

  FocusNode inputNode = FocusNode();

  /**
      attr before terminer validation filter
   */

  String before_filter_idcategorie = "";
  String before_filter_id_ville_selected = "0";
  String before_filter_ville_name = "";
  bool before_filter_ischecked_1 = false;
  bool before_filter_ischecked_2 = false;
  bool before_filter_ischecked_3 = false;
  bool before_filter_ischecked_4 = false;
  bool before_filter_ischecked_5 = false;
  bool before_filter_ischecked_6 = false;
  List<String> before_filter_list_attribute = [];
  List<String> before_filter_list_id_attribute = [];
  List<DataAttribute> before_filter_DataAttribute = [];
  int before_filter_prix_min = 0;

  int before_filter_prix_max = 0;

  List<Categorie> before_filter_front_postpathcategori = [];

  @override
  void initState() {
    super.initState();
    if(widget.order == "2"){
       titleorderby = "Dernière minute";
       orderby = "2";
    }
    text_search = widget.text_search;
    idcategorie = widget.id_categorie;
    getFirstMaxPrice(widget.id_categorie);
    ListProduct = [];
    more_item = true;
    loader_bottom = false;
    getlistProduct_filtred();
    villelist.insert(0, ville_selected);
    villelist_child.insert(0, ville_child_selected);
    //ville_selected = villelist.first;
  }

  @override
  Widget build(BuildContext context) {
    final themeBrightness = Theme.of(context).brightness;
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: Scaffold(
          appBar: AppBar(
            surfaceTintColor: Colors.white,
            elevation: 3,
            shadowColor: Colors.black,
            backgroundColor: Colors.white,
            title: const Text("Offres"),
            actions: [
              IconButton(
                  onPressed: () {
                    _scaffoldKey.currentState!.openEndDrawer();
                  },
                  icon: const Icon(Icons.search))
            ],
          ),
          key: _scaffoldKey,
          endDrawer: Drawer(
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.only(topRight: Radius.circular(0), bottomRight: Radius.circular(0)),
            ),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Stack(
                children: [
                  SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextField(
                          controller: _controllerTextSearch,
                          decoration: InputDecoration(
                            hintText: 'Recherche des produits',
                            suffixIcon: IconButton(
                              onPressed: _controllerTextSearch.clear,
                              icon: const Icon(Icons.clear),
                            ),
                            border: const OutlineInputBorder(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(5.0),
                                bottomLeft: Radius.circular(5.0),
                                bottomRight: Radius.circular(5.0),
                                topRight: Radius.circular(5.0),
                              ),
                            ),
                          ),
                        ),
                        const Padding(
                          padding: EdgeInsets.all(3.0),
                          child: Text("Emplacement", style: TextStyle(fontWeight: FontWeight.bold)),
                        ),
                        DropdownMenu<Ville>(
                          initialSelection: ville_selected,
                          onSelected: (Ville? value) {
                            // This is called when the user selects an item.
                            setState(() {
                              if (value!.id == "0") {
                                villelist_child.clear();
                                villelist_child.add(Ville(id: "0", nom: "--Gouvernorate--", idParent: "0"));
                              } else {
                                villelist_child = Ville.get_all_ville_child(value.id!);
                              }
                              ville_selected = value;
                              before_filter_id_ville_selected = value.id!;
                              before_filter_ville_name = value.nom!;
                              ville_principale_selected = value;
                            });
                          },
                          expandedInsets: const EdgeInsets.all(2.0),
                          dropdownMenuEntries: villelist.map<DropdownMenuEntry<Ville>>((Ville value) {
                            return DropdownMenuEntry<Ville>(
                              value: value,
                              label: value.nom!,
                            );
                          }).toList(),
                        ),
                        const Padding(padding: EdgeInsets.symmetric(vertical: 2)),
                        DropdownMenu<Ville>(
                          initialSelection: ville_child_selected,
                          onSelected: (Ville? value) {
                            // This is called when the user selects an item.
                            setState(() {
                              ville_child_selected = value!;
                              before_filter_id_ville_selected = value.id!;
                              if (value.nom! == "Tous") {
                                before_filter_ville_name = ville_principale_selected.nom!;
                              } else {
                                before_filter_ville_name = value.nom!;
                              }
                            });
                          },
                          expandedInsets: const EdgeInsets.all(2.0),
                          dropdownMenuEntries: villelist_child.map<DropdownMenuEntry<Ville>>((Ville value) {
                            return DropdownMenuEntry<Ville>(
                              value: value,
                              label: value.nom!,
                            );
                          }).toList(),
                        ),
                        const Padding(
                          padding: EdgeInsets.all(3.0),
                          child: Text("Catégorie", style: TextStyle(fontWeight: FontWeight.bold)),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Tags(
                              spacing: 1,
                              runSpacing: 1,
                              alignment: WrapAlignment.start,
                              itemCount: postpathcategorie.length,
                              itemBuilder: (int index) {
                                return TextButton(
                                  style: TextButton.styleFrom(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(3),
                                    ),
                                    padding: const EdgeInsets.all(10),
                                    minimumSize: const Size(0, 0),
                                    elevation: 0,
                                  ),
                                  onPressed: () {
                                    Future<List<Categorie>> postdata = getListCategorie("${postpathcategorie[index].id}");
                                    postdata.then((value) {
                                      if (value.isNotEmpty) {
                                        setState(() {
                                          postcategorie = postdata;
                                          getPathListCategorie("${postpathcategorie[index].id}");
                                        });
                                      }
                                    });
                                  },
                                  child: Text("${postpathcategorie[index].nomCategorieProduit} >",
                                      style: const TextStyle(color: Colors.black, fontSize: 11, fontWeight: FontWeight.normal)),
                                );
                              },
                            ),
                            const Divider(color: Colors.black87),
                            FutureBuilder(
                              builder: (ctx, snapshot) {
                                if (snapshot.connectionState == ConnectionState.waiting) {
                                  return const CircularProgressIndicator();
                                } else if (snapshot.hasData) {
                                  final posts = snapshot.data!;
                                  return Tags(
                                    spacing: 2,
                                    runSpacing: 2,
                                    alignment: WrapAlignment.start,
                                    itemCount: snapshot.data!.length,
                                    // required
                                    itemBuilder: (int index) {
                                      final item = snapshot.data![index];
                                      return ElevatedButton(
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: ColorConstant.abonne_enchere,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(3),
                                          ),
                                          padding: const EdgeInsets.all(10),
                                          minimumSize: const Size(0, 0),
                                          elevation: 0,
                                        ),
                                        onPressed: () {
                                          Future<List<Categorie>> postdata = getListCategorie("${item.id}");
                                          postdata.then((value) {
                                            if (value.isNotEmpty) {
                                              if (item.idTypeProduit != 0) {
                                                setState(() {
                                                  id_type_produit = int.parse(item.idTypeProduit!);
                                                  getListAttribute("${id_type_produit}");
                                                  before_filter_idcategorie = "${item.id}";
                                                });
                                              }
                                              setState(() {
                                                postcategorie = postdata;
                                                getPathListCategorie("${item.id}");
                                              });
                                            } else {
                                              if (item.idTypeProduit != 0) {
                                                setState(() {
                                                  id_type_produit = int.parse(item.idTypeProduit!);
                                                  getListAttribute("${id_type_produit}");
                                                  before_filter_idcategorie = "${item.id}";
                                                  getPathListCategorie("${item.id}");
                                                });
                                              }
                                              setState(() {
                                                postcategorie = singletag(item);
                                              });
                                            }
                                          });
                                        },
                                        child: Text(item.nomCategorieProduit!,
                                            style: const TextStyle(color: Color(0xFFFFFFFF), fontSize: 11, fontWeight: FontWeight.normal)),
                                      );
                                    },
                                  );
                                } else {
                                  return const Text("No data available");
                                }
                              },
                              future: postcategorie,
                            ),
                          ],
                        ),
                        attributelist.length != 0
                            ? ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: attributelist.length,
                                itemBuilder: (context, index) {
                                  return get_widget_par_type(attributelist[index]);
                                },
                              )
                            : const SizedBox(),
                        const Padding(
                          padding: EdgeInsets.all(3.0),
                          child: Text("Prix", style: TextStyle(fontWeight: FontWeight.bold)),
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(
                              child: FlutterSlider(
                                values: new_currentRangeValues,
                                rangeSlider: true,
                                max: Maxrange,
                                min: Minrange,
                                step: FlutterSliderStep(step: 1),
                                jump: true,
                                onDragCompleted: (handlerIndex, lowerValue, upperValue) {
                                  setState(() {
                                    new_currentRangeValues = [lowerValue, upperValue];
                                  });

                                  before_filter_prix_min = lowerValue.round();
                                  before_filter_prix_max = upperValue.round();
                                },
                                onDragging: (handlerIndex, lowerValue, upperValue) {},
                              ),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [Text("${new_currentRangeValues[0]}"), Text("${new_currentRangeValues[1]}")],
                            )
                          ],
                        ),
                        const Padding(
                          padding: EdgeInsets.all(3.0),
                          child: Text("État", style: TextStyle(fontWeight: FontWeight.bold)),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: <Widget>[
                            Checkbox(
                              value: before_filter_ischecked_1,
                              onChanged: (bool? value) {
                                setState(() {
                                  before_filter_ischecked_1 = value!;
                                });
                              },
                            ),
                            const Text("D'occasion"),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: <Widget>[
                            Checkbox(
                              value: before_filter_ischecked_2,
                              onChanged: (bool? value) {
                                setState(() {
                                  before_filter_ischecked_2 = value!;
                                });
                              },
                            ),
                            const Text("Neuf avec emballage d'origine"),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: <Widget>[
                            Checkbox(
                              value: before_filter_ischecked_3,
                              onChanged: (bool? value) {
                                setState(() {
                                  before_filter_ischecked_3 = value!;
                                });
                              },
                            ),
                            const Text("Neuf"),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: <Widget>[
                            Checkbox(
                              value: before_filter_ischecked_4,
                              onChanged: (bool? value) {
                                setState(() {
                                  before_filter_ischecked_4 = value!;
                                });
                              },
                            ),
                            const Text("Défectueux"),
                          ],
                        ),
                        const Padding(
                          padding: EdgeInsets.all(3.0),
                          child: Text("Type de vente", style: TextStyle(fontWeight: FontWeight.bold)),
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: <Widget>[
                            Checkbox(
                              value: before_filter_ischecked_5,
                              onChanged: (bool? value) {
                                setState(() {
                                  before_filter_ischecked_5 = value!;
                                });
                              },
                            ),
                            const Text("Enchére"),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: <Widget>[
                            Checkbox(
                              value: before_filter_ischecked_6,
                              onChanged: (bool? value) {
                                setState(() {
                                  before_filter_ischecked_6 = value!;
                                });
                              },
                            ),
                            const Text("Avec achat direct"),
                          ],
                        ),
                        const SizedBox(
                          height: 60,
                        )
                      ],
                    ),
                  ),
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: Container(
                      height: 50,
                      decoration: const BoxDecoration(color: Colors.white),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 5,
                            child: InkWell(
                              child: Padding(
                                padding: const EdgeInsets.only(left: 4, top: 4, right: 2, bottom: 2),
                                child: Container(
                                    height: 50,
                                    decoration: BoxDecoration(color: ColorConstant.track_scroll, borderRadius: BorderRadius.circular(7.0)),
                                    child: const Center(
                                        child: Text("ANNULÉ", style: TextStyle(color: Colors.black, fontSize: 11, fontWeight: FontWeight.normal)))),
                              ),
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                            ),
                          ),
                          Expanded(
                            flex: 5,
                            child: InkWell(
                                child: Padding(
                                  padding: const EdgeInsets.only(left: 2, top: 4, right: 4, bottom: 2),
                                  child: Container(
                                      height: 50,
                                      decoration: BoxDecoration(color: ColorConstant.second_color, borderRadius: BorderRadius.circular(7.0)),
                                      child: const Center(
                                          child:
                                              Text("TERMINÉ", style: TextStyle(color: Colors.white, fontSize: 11, fontWeight: FontWeight.normal)))),
                                ),
                                onTap: () {
                                  idcategorie = before_filter_idcategorie;
                                  id_ville_selected = before_filter_id_ville_selected;
                                  ville_name = before_filter_ville_name;
                                  ischecked_1 = before_filter_ischecked_1;
                                  ischecked_2 = before_filter_ischecked_2;
                                  ischecked_3 = before_filter_ischecked_3;
                                  ischecked_4 = before_filter_ischecked_4;
                                  ischecked_5 = before_filter_ischecked_5;
                                  ischecked_6 = before_filter_ischecked_6;
                                  list_attribute = before_filter_list_attribute;
                                  list_id_attribute = before_filter_list_id_attribute;
                                  prix_min = before_filter_prix_min;
                                  prix_max = before_filter_prix_max;
                                  text_search = _controllerTextSearch.text;
                                  if (before_filter_front_postpathcategori.isEmpty) {
                                    front_postpathcategorie = initfront_path_categorie();
                                  } else {
                                    front_postpathcategorie = before_filter_front_postpathcategori;
                                  }
                                  ListProduct.clear();
                                  getlistProduct_filtred();
                                  Navigator.of(context).pop();
                                }),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          body: NestedScrollView(
            headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
              return [
                SliverAppBar(
                  flexibleSpace: FlexibleSpaceBar(
                      background: Container(
                    child: Column(children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              child: Row(
                                children: [
                                  Text(titleorderby, style: TextStyle(color: ColorConstant.second_color)),
                                  Icon(Icons.keyboard_arrow_down, color: ColorConstant.second_color)
                                ],
                              ),
                              onTap: () {
                                bottomsheetorderby();
                              },
                            ),
                            IntrinsicHeight(
                              child: Row(
                                children: [
                                  GestureDetector(
                                    child: Row(
                                      children: [
                                        Icon(Icons.location_pin, color: ColorConstant.second_color),
                                        Text(ville_name == "" ? "Ville" : ville_name, style: TextStyle(color: ColorConstant.second_color)),
                                      ],
                                    ),
                                    onTap: () {
                                      _navigateAndDisplaySelection(context);
                                    },
                                  ),
                                  const VerticalDivider(
                                    endIndent: 1,
                                    indent: 1,
                                    color: Colors.black,
                                    thickness: 0.5,
                                  ),
                                  GestureDetector(
                                    child: Row(
                                      children: [
                                        Icon(Icons.filter_alt_sharp, color: ColorConstant.second_color),
                                        Text("Filter", style: TextStyle(color: ColorConstant.second_color)),
                                      ],
                                    ),
                                    onTap: () {
                                      Scaffold.of(context).openEndDrawer();
                                    },
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 5.0, right: 5.0),
                        child: Row(
                          children: [
                            Tags(
                              spacing: 1,
                              runSpacing: 1,
                              alignment: WrapAlignment.start,
                              itemCount: front_postpathcategorie.length,
                              itemBuilder: (int index) {
                                return TextButton(
                                  style: TextButton.styleFrom(
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(3),
                                    ),
                                    padding: const EdgeInsets.all(10),
                                    minimumSize: const Size(0, 0),
                                    elevation: 0,
                                  ),
                                  onPressed: () {
                                    Scaffold.of(context).openEndDrawer();
                                  },
                                  child: Text("${front_postpathcategorie[index].nomCategorieProduit} >",
                                      style: const TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.normal)),
                                );
                              },
                            ),
                          ],
                        ),
                      )
                    ]),
                  )),
                  actions: <Widget>[Container()],
                  surfaceTintColor: Colors.white,
                  backgroundColor: Colors.white,
                  automaticallyImplyLeading: false,
                  expandedHeight: 90,
                  floating: true,
                  snap: true,
                  pinned: false,
                  shadowColor: Colors.black,
                ),
              ];
            },
            body: NotificationListener<ScrollEndNotification>(
                onNotification: (scrollEnd) {
                  final metrics = scrollEnd.metrics;
                  if (metrics.atEdge) {
                    bool isTop = metrics.pixels == 0;
                    if (isTop) {
                    } else {
                      if (more_item == true) {
                        setState(() {
                          more_item = false;
                          getlistProduct_filtred();
                          loader_bottom = true;
                        });
                      }
                    }
                  }
                  return true;
                },
                child: Stack(
                  children: [
                    gridlistProduct(),
                    loader_bottom == true
                        ? Positioned(
                            bottom: 0,
                            child: Container(
                              color: Colors.white,
                              height: 50,
                              width: MediaQuery.of(context).size.width,
                              child: const Center(
                                child: Text(
                                  'Chargement...',
                                  style: TextStyle(fontSize: 18, fontFamily: 'Araboto'),
                                ),
                              ),
                            ))
                        : SizedBox(),
                  ],
                )),
          )),
    );
  }

  Widget gridlistProduct() {
    if (ListProduct.isEmpty) {
      if (more_item == true) {
        return GridView.builder(
            gridDelegate:
                const SliverGridDelegateWithMaxCrossAxisExtent(mainAxisExtent: 250, maxCrossAxisExtent: 250, crossAxisSpacing: 0, mainAxisSpacing: 0),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 12,
            itemBuilder: (BuildContext ctx, index) {
              return const ItemProductShimmer();
            });
      } else {
        return const Center(child: Text("Aucune offre pour l\'instant"));
      }
    } else {
      return GridView.builder(
          padding: const EdgeInsets.only(top: 20),
          gridDelegate:
              const SliverGridDelegateWithMaxCrossAxisExtent(mainAxisExtent: 280, maxCrossAxisExtent: 250, crossAxisSpacing: 0, mainAxisSpacing: 0),
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: ListProduct.length,
          itemBuilder: (BuildContext ctx, index) {
            return Itemrandomproduct(snapshot: ListProduct.elementAt(index), context: context, full: 2);
          });
    }
  }

  Future<void> getlistProduct_filtred() async {
    List<Product> newlistproduct = [];

    Map<String, String> queryParams = {
      'orderby': orderby,
      'start': "${ListProduct.length}",
      'limit': "12",
      'categorie': idcategorie,
    };

    for (int i = 0; i < list_attribute.length; i++) {
      var key = list_attribute.elementAt(i);
      queryParams['offre_attr_${key}'] = list_id_attribute[i];
    }
    queryParams['ville'] = id_ville_selected;

    if (text_search != "") {
      queryParams['text_search'] = text_search;
    }

    if(prix_max != 0) {
      queryParams['prix_produit[1]'] =  "${prix_min}";
      queryParams['prix_produit[2]'] =  "${prix_max}";
    }

    if (ischecked_1 != false) {
      queryParams["offre_etat[1]"] = "1";
    }
    if (ischecked_2 != false) {
      queryParams["offre_etat[2]"] = "2";
    }
    if (ischecked_3 != false) {
      queryParams["offre_etat[3]"] = "3";
    }
    if (ischecked_4 != false) {
      queryParams["offre_etat[4]"] = "4";
    }

    if (ischecked_5 != false) {
      queryParams["offre_type_vente[1]"] = convert_bool_to_string_of_int(ischecked_5);
    }
    if (ischecked_6 != false) {
      queryParams["offre_type_vente[2]"] = convert_bool_to_string_of_int(ischecked_6);
    }

    var headers = {
      HttpHeaders.contentTypeHeader: 'application/json',
    };

    String queryString = Uri(queryParameters: queryParams).query;

    var requestUrl = '${StringConstant.base_url}api/get_list_product_filter.php?$queryString';
    var res = await http.get(Uri.parse(requestUrl), headers: headers);

    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      newlistproduct = List<Product>.from(l.map((model) => Product.fromJson(model)));
      setState(() {
        if (newlistproduct.length != 12) {
          more_item = false;
        } else {
          more_item = true;
        }
      });
      setState(() {
        ListProduct.addAll(newlistproduct);
        loader_bottom = false;
      });
    } else {}
  }

  String convert_bool_to_string_of_int(bool _bool) {
    if (_bool == true) {
      return "1";
    } else {
      return "0";
    }
  }

  _navigateAndDisplaySelection(BuildContext context) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SelectionVille()),
    );

    if (result != null) {
      id_ville_selected = result[0];
      setState(() {
        ville_name = result[1];
      });
      initializelist();
    }
  }

  Future bottomsheetorderby() {
    return showModalBottomSheet(
        context: context,
        builder: (context) {
          return Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                ListTile(
                  trailing: orderby == "1" ? Image.asset("assets/images/drop_down_menu_ic_v.png") : null,
                  title: const Text('Nouveaux'),
                  onTap: () {
                    orderby = "1";
                    setState(() {
                      titleorderby = 'Nouveaux';
                    });
                    initializelist();
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  trailing: orderby == "2" ? Image.asset("assets/images/drop_down_menu_ic_v.png") : null,
                  title: const Text('Dernière minute'),
                  onTap: () {
                    orderby = "2";
                    setState(() {
                      titleorderby = 'Dernière minute';
                    });
                    initializelist();
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  trailing: orderby == "3" ? Image.asset("assets/images/drop_down_menu_ic_v.png") : null,
                  title: const Text('Enchéres'),
                  onTap: () {
                    orderby = "3";
                    setState(() {
                      titleorderby = 'Enchéres';
                    });
                    initializelist();
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  trailing: orderby == "4" ? Image.asset("assets/images/drop_down_menu_ic_v.png") : null,
                  title: const Text('Le moins cher'),
                  onTap: () {
                    orderby = "4";
                    setState(() {
                      titleorderby = 'Le moins cher';
                    });
                    initializelist();
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  trailing: orderby == "5" ? Image.asset("assets/images/drop_down_menu_ic_v.png") : null,
                  title: const Text('Le plus cher'),
                  onTap: () {
                    orderby = "5";
                    setState(() {
                      titleorderby = 'Le plus cher';
                    });
                    initializelist();
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          );
        });
  }

  void initializelist() {
    ListProduct.clear();
    more_item = true;
    loader_bottom = false;
    getlistProduct_filtred();
  }

  Widget get_widget_par_type(TypeAttribute type_attr) {
    switch (type_attr.typeAttribute) {
      case "1": // 2 list deroulant
        return complex_liste_deroulant(type_attr);
        break;
      case "2": // sample list deroulant
        return simple_liste_deroulant(type_attr);
        break;
      case "3": // couleur
        return list_choix_couleur(type_attr);
        break;
      case "4": //date

        listtextdate.insert(date_counter, TextEditingController());
        listtextdate.insert(date_counter + 1, TextEditingController());
        Widget widget = date_picker(type_attr, date_counter);
        date_counter = date_counter + 2;
        return widget;
        break;
      case "5": // spinner range
        double maxrangeattr = double.parse(type_attr.dataAttribute!.first.nomAttributeValue!);
        if (!list_range_slider.containsKey("${type_attr.idAttribute}")) {
          list_range_slider[type_attr.idAttribute!] = [0, maxrangeattr.toDouble()];
          //slide_controller.add_to_list_range_slider(type_attr.idAttribute!, [0, maxrange.toDouble()]);
        }
        return range_slider(type_attr, 0.0, maxrangeattr);
        break;
    }
    return SizedBox();
  }

  static Future<List<Categorie>> getListCategorie(String cat) async {
    List<Categorie> listcategorie = [];
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_list_categorie.php?categorie=${cat}'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      List<Categorie> listcategorie = List<Categorie>.from(l.map((model) => Categorie.fromJson(model)));
      return listcategorie;
    } else {
      return listcategorie;
    }
  }

  static Future<List<Categorie>> singletag(Categorie cat) async {
    List<Categorie> listcategorie = [];
    listcategorie.add(cat);
    return listcategorie;
  }

  void initgetListCategorie(String cat) async {
    List<Categorie> listcategorie = [];
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_list_categorie.php?categorie=${cat}'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      List<Categorie> listcategorie = List<Categorie>.from(l.map((model) => Categorie.fromJson(model)));
      setState(() {
        postcategorie = listcategorie as Future<List<Categorie>>;
      });
    }
  }

  static List<Categorie> initpathcategorie() {
    List<Categorie> listpathcat = [];
    Categorie cat = Categorie(id: "0", nomCategorieProduit: "Tous", icon: "", idTypeProduit: "0");
    listpathcat.add(cat);
    return listpathcat;
  }

  void getPathListCategorie(String cat) async {
    List<Categorie> listcategorie = initpathcategorie();
    List<Categorie> listcategorie_front_path = [];
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_prix_and_categorie.php?categorie=${cat}'));
    if (res.statusCode == 200) {
      PrixAndCategorie listpathcategorie = PrixAndCategorie.fromJson(json.decode(res.body));
      listcategorie.addAll(listpathcategorie.categoriePath as Iterable<Categorie>);
      listcategorie_front_path.addAll(listpathcategorie.categoriePath as Iterable<Categorie>);
      setState(() {
        Maxrange = double.parse(listpathcategorie.prixProduitMax!);
        new_currentRangeValues = [0.0, Maxrange];
        postpathcategorie = [];
        postpathcategorie = listcategorie;
        before_filter_front_postpathcategori = listcategorie_front_path;
      });
    }
  }

  getListAttribute(String id_attr) async {
    List<TypeAttribute> listtypeattributee = [];
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_list_attribute_type.php?id_type_produit=${id_attr}'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      listtypeattributee = List<TypeAttribute>.from(l.map((model) => TypeAttribute.fromJson(model)));
      setState(() {
        attributelist = listtypeattributee;
      });
    } else {
      attributelist = listtypeattributee;
    }
  }

  Widget simple_liste_deroulant(TypeAttribute type_attr) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.all(3.0),
          child: Text(
            "${type_attr.nomAttribute}",
            style: TextStyle(fontWeight: FontWeight.bold),
            textAlign: TextAlign.start,
          ),
        ),
        DropdownMenu<DataAttribute>(
          initialSelection: null,
          hintText: "--${type_attr.nomAttribute}--",
          onSelected: (DataAttribute? value) {
            // This is called when the user selects an item.
            if (!before_filter_list_attribute.contains(type_attr.idAttribute)) {
              before_filter_list_attribute.add(type_attr.idAttribute!);
              before_filter_list_id_attribute.add("${value?.id}");
            } else {
              int position_attribte = before_filter_list_attribute.indexOf("${type_attr.idAttribute}");
              before_filter_list_id_attribute[position_attribte] = "${value?.id}";
            }
          },
          expandedInsets: const EdgeInsets.all(2.0),
          dropdownMenuEntries: type_attr.dataAttribute!.map<DropdownMenuEntry<DataAttribute>>((DataAttribute value) {
            return DropdownMenuEntry<DataAttribute>(
              value: value,
              label: value.nomAttributeValue!,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget list_choix_couleur(TypeAttribute type_attr) {
    List<DataAttribute> dataAttributes = type_attr.dataAttribute!;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(3.0),
          child: Text(
            "${type_attr.nomAttribute}",
            style: const TextStyle(fontWeight: FontWeight.bold),
            textAlign: TextAlign.start,
          ),
        ),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 5,
            mainAxisSpacing: 2,
            crossAxisSpacing: 2,
            childAspectRatio: 1,
          ),
          itemCount: dataAttributes.length,
          itemBuilder: (context, index) {
            DataAttribute data = dataAttributes[index];
            int id = int.parse(data.id!);

            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(7.0),
                gradient: data.id == "9"
                    ? const LinearGradient(
                        colors: [Colors.purple, Colors.red, Colors.orange, Colors.yellow, Colors.blue],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter)
                    : data.id == "19"
                        ? const RadialGradient(radius: 1.0, colors: [Colors.white, Colors.black87])
                        : const LinearGradient(colors: [Colors.transparent, Colors.transparent]),
              ),
              child: FilledButton(
                  onPressed: () {
                    setState(() {
                      id_color = data.id!;
                      if (!before_filter_list_attribute.contains(type_attr.idAttribute)) {
                        before_filter_list_attribute.add("${type_attr.idAttribute}");
                        before_filter_list_id_attribute.add("${id}");
                      } else {
                        int position_attribte = before_filter_list_attribute.indexOf("${type_attr.idAttribute}");
                        before_filter_list_id_attribute[position_attribte] = "${id}";
                      }
                    });
                  },
                  style: ButtonStyle(
                    backgroundColor: data.id == "9"
                        ? WidgetStateProperty.all(Colors.transparent) // replace with your gradient color
                        : data.id == "19"
                            ? WidgetStateProperty.all(Colors.transparent) // replace with your other color
                            : WidgetStateProperty.all(HexColor(getHexaColor(data.nomAttributeValue!))),
                    shape: WidgetStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(7.0),
                        side: data.id == "3" ? const BorderSide(color: Colors.black) : const BorderSide(color: Colors.transparent))),
                    padding: WidgetStateProperty.all(const EdgeInsets.all(1.0)),
                  ),
                  child: id_color == data.id
                      ? SizedBox(width: 50, child: Icon(Icons.check, size: 22, color: HexColor(getHexaTextColor(data.nomAttributeValue!))))
                      : const SizedBox()),
            );
          },
        ),
      ],
    );
  }

  String getHexaColor(String name) {
    String colorHexa = "#ffffff";
    switch (name) {
      case "silver":
        colorHexa = "#C0C0C0";
        break;
      case "beige":
        colorHexa = "#F5F5DC";
        break;
      case "white":
        colorHexa = "#FFFFFF";
        break;
      case "blue":
        colorHexa = "#0000FF";
        break;
      case "gold":
        colorHexa = "#FFD700";
        break;
      case "grey":
        colorHexa = "#808080";
        break;
      case "yellow":
        colorHexa = "#FFFF00";
        break;
      case "brown":
        colorHexa = "#964B00";
        break;
      case "multicolor":
        colorHexa = "#FFFFFF";
        break;
      case "black":
        colorHexa = "#000000";
        break;
      case "orange":
        colorHexa = "#FFA500";
        break;
      case "pink":
        colorHexa = "#FFC0CB";
        break;
      case "red":
        colorHexa = "#FF0000";
        break;
      case "taupe":
        colorHexa = "#483C32";
        break;
      case "turquoise":
        colorHexa = "#30D5C8";
        break;
      case "green":
        colorHexa = "#00FF00";
        break;
      case "olive":
        colorHexa = "#808000";
        break;
      case "purple":
        colorHexa = "#800080";
        break;
      case "other":
        colorHexa = "#FFFFFF";
        break;
      default:
        colorHexa = "#FFFFFF";
    }
    return colorHexa;
  }

  String getHexaTextColor(String name) {
    String colorHexa = "#ffffff";
    switch (name) {
      case "silver":
      case "beige":
      case "white":
      case "yellow":
      case "other":
        colorHexa = "#000000";
        break;
      case "blue":
      case "gold":
      case "grey":
      case "brown":
      case "black":
      case "orange":
      case "pink":
      case "red":
      case "taupe":
      case "turquoise":
      case "green":
      case "olive":
      case "purple":
      case "multicolor":
        colorHexa = "#FFFFFF";
        break;
      default:
        colorHexa = "#FFFFFF";
    }
    return colorHexa;
  }

  Widget date_picker(TypeAttribute type_attr, int date_counter) {
    TextEditingController TextEditingController1 = listtextdate.elementAt(date_counter);
    TextEditingController TextEditingController2 = listtextdate.elementAt(date_counter + 1);
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(3.0),
          child: Text(
            "${type_attr.nomAttribute}",
            style: const TextStyle(fontWeight: FontWeight.bold),
            textAlign: TextAlign.start,
          ),
        ),
        Container(
            height: 50,
            decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.black,
                ),
                borderRadius: const BorderRadius.all(Radius.circular(5))),
            padding: EdgeInsets.all(7),
            //height: MediaQuery.of(context).size.width / 3,
            child: Center(
                child: TextField(
              textAlignVertical: TextAlignVertical.center,
              controller: TextEditingController1,
              //editing controller of this TextField
              decoration: const InputDecoration(
                  icon: Icon(Icons.calendar_today),
                  hintText: "Du : jj/mm/aaaa",
                  border: InputBorder.none,
              ),
              readOnly: true,
              //set it true, so that user will not able to edit text
              onTap: () async {
                DateTime? pickedDate = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(1950),
                    //DateTime.now() - not to allow to choose before today.
                    lastDate: DateTime(2100));

                if (pickedDate != null) {
                  String formattedDate = DateFormat('dd-MM-yyyy').format(pickedDate);
                  TextEditingController1.text = formattedDate;
                } else {}
              },
            ))),
        const SizedBox(height: 5),
        Container(
            height: 50,
            decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.black,
                ),
                borderRadius: const BorderRadius.all(Radius.circular(5))),
            padding: EdgeInsets.all(7),
            //height: MediaQuery.of(context).size.width / 3,
            child: Center(
                child: TextField(
              textAlignVertical: TextAlignVertical.center,
              controller: TextEditingController2,
              //editing controller of this TextField
              decoration: const InputDecoration(
                icon: Icon(Icons.calendar_today),
                hintText: "Jusqu'au : jj/mm/aaaa",
                border: InputBorder.none,
              ),
              readOnly: true,
              //set it true, so that user will not able to edit text
              onTap: () async {
                DateTime? pickedDate = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(1950),
                    //DateTime.now() - not to allow to choose before today.
                    lastDate: DateTime(2100));

                if (pickedDate != null) {
                  String formattedDate = DateFormat('dd-MM-yyyy').format(pickedDate);
                  TextEditingController2.text = formattedDate;
                  /*setState(() {
                          TextEditingController2.text = formattedDate; //set output date to TextField value.
                        });*/
                } else {}
              },
            ))),
      ],
    );
  }

  Widget complex_liste_deroulant(TypeAttribute type_attr) {
    if (before_filter_list_attribute.contains(type_attr.idAttribute)) {
      initdataattr = before_filter_DataAttribute.elementAt(before_filter_list_attribute.indexOf("${type_attr.idAttribute}"));
      existinit = true;
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(3.0),
          child: Text(
            "${type_attr.nomAttribute}",
            style: const TextStyle(fontWeight: FontWeight.bold),
            textAlign: TextAlign.start,
          ),
        ),
        DropdownMenu<DataAttribute>(
          hintText: "--${type_attr.nomAttribute}--",
          initialSelection: existinit == true ? initdataattr : null,
          onSelected: (DataAttribute? value) {
            if (!before_filter_list_attribute.contains(type_attr.idAttribute)) {
              before_filter_list_attribute.add(type_attr.idAttribute!);
              before_filter_list_id_attribute.add("${value?.id}");
              before_filter_DataAttribute.add(value!);
            } else {
              int position_attribte = before_filter_list_attribute.indexOf("${type_attr.idAttribute}");
              before_filter_list_id_attribute[position_attribte] = "${value?.id}";
              before_filter_DataAttribute[position_attribte] = value!;
            }
            get_list_sous_attribute_type("${value?.id}");
          },
          expandedInsets: const EdgeInsets.all(2.0),
          dropdownMenuEntries: type_attr.dataAttribute!.map<DropdownMenuEntry<DataAttribute>>((DataAttribute value) {
            return DropdownMenuEntry<DataAttribute>(
              value: value,
              label: value.nomAttributeValue!,
            );
          }).toList(),
        ),
        const SizedBox(height: 5),
        sousattributelist.dataAttribute != null
            ? DropdownMenu<DataAttribute>(
                initialSelection: sousattributelist.dataAttribute?.first,
                hintText: "--choisir--",
                onSelected: (DataAttribute? value) {
                  // This is called when the user selects an item.
                  if (!before_filter_list_attribute.contains(type_attr.idAttribute)) {
                    before_filter_list_attribute.add(type_attr.idAttribute!);
                    before_filter_list_id_attribute.add("${value?.id}");
                  } else {
                    int position_attribte = before_filter_list_attribute.indexOf("${type_attr.idAttribute}");
                    before_filter_list_id_attribute[position_attribte] = "${value?.id}";
                  }
                },
                expandedInsets: const EdgeInsets.all(2.0),
                dropdownMenuEntries: sousattributelist.dataAttribute!.map<DropdownMenuEntry<DataAttribute>>((DataAttribute value) {
                  return DropdownMenuEntry<DataAttribute>(
                    value: value,
                    label: value.nomAttributeValue!,
                  );
                }).toList())
            : const SizedBox(),
      ],
    );
  }

  void get_list_sous_attribute_type(String cat) async {
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_list_sous_attribute_type.php?datas=${cat}'));
    if (res.statusCode == 200) {
      TypeAttribute _sousattributelist = TypeAttribute.fromJson(json.decode(res.body));
      setState(() {
        sousattributelist = _sousattributelist;
      });
    }
  }

  Widget range_slider(TypeAttribute type_attr, double min, double maxval) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.all(3.0),
          child: Text(
            "${type_attr.nomAttribute}",
            style: const TextStyle(fontWeight: FontWeight.bold),
            textAlign: TextAlign.start,
          ),
        ),
        SizedBox(
          child: FlutterSlider(
            values: list_range_slider[type_attr.idAttribute!]!,
            rangeSlider: true,
            max: maxval,
            min: 0,
            step: FlutterSliderStep(step: 1),
            jump: true,
            onDragCompleted: (handlerIndex, lowerValue, upperValue) {
              setState(() {
                list_range_slider[type_attr.idAttribute!] = [lowerValue, upperValue];
              });

              if (!before_filter_list_attribute.contains("${type_attr.idAttribute}[1]")) {
                before_filter_list_attribute.add("${type_attr.idAttribute}[1]");
                int p1 = before_filter_list_attribute.indexOf("${type_attr.idAttribute}[1]");
                before_filter_list_id_attribute.insert(p1, "${lowerValue}");

                before_filter_list_attribute.add("${type_attr.idAttribute}[2]");
                int p2 = before_filter_list_attribute.indexOf("${type_attr.idAttribute}[2]");
                before_filter_list_id_attribute.insert(p2, "${upperValue}");
              } else {
                int position_attribte = before_filter_list_attribute.indexOf("${type_attr.idAttribute}[1]");
                before_filter_list_id_attribute[position_attribte] = "${lowerValue}";

                int position_attribte1 = before_filter_list_attribute.indexOf("${type_attr.idAttribute}[2]");
                before_filter_list_id_attribute[position_attribte1] = "${upperValue}";
              }
            },
            onDragging: (handlerIndex, lowerValue, upperValue) {},
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [Text("${list_range_slider[type_attr.idAttribute!]![0]}"), Text("${list_range_slider[type_attr.idAttribute!]![1]}")],
        )
      ],
    );
  }

  static List<Categorie> initfront_path_categorie() {
    List<Categorie> listcat = [];
    listcat.add(Categorie(id: "0", nomCategorieProduit: "Toutes les catégories", icon: "", idTypeProduit: "0"));
    return listcat;
  }



  void getFirstMaxPrice(String cat) async {
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_prix_and_categorie.php?categorie=${cat}'));
    if (res.statusCode == 200) {
      PrixAndCategorie listpathcategorie = PrixAndCategorie.fromJson(json.decode(res.body));
      setState(() {
        Maxrange = double.parse(listpathcategorie.prixProduitMax!);
        prix_max = Maxrange.round() ;
        new_currentRangeValues = [0.0, Maxrange];
      });
    }
  }




}
