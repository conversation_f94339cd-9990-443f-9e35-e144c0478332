import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:image_picker/image_picker.dart';
import 'package:my_application/Model/response_request.dart';
import 'package:my_application/Model/ville.dart';
import 'package:my_application/shimmer_effect/build_interface_profil_shimmer.dart';
import 'package:my_application/utils/check_authenticate.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:http/http.dart' as http;
import 'package:my_application/utils/list_search_sharedprefrence.dart';
import 'package:my_application/utils/string_constant.dart';
import 'package:my_application/utils/wishlist_product_sharedprefrence.dart';
import 'package:my_application/utils/wishlist_store_sharedprefrence.dart';
import 'package:my_application/widget/show_custom_loader_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http_parser/http_parser.dart';
import 'Model/profil.dart';

class ProfilDetail extends StatefulWidget {
  const ProfilDetail({super.key});

  @override
  State<ProfilDetail> createState() => _ProfilDetailState();
}

class _ProfilDetailState extends State<ProfilDetail> {
  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
  Profil profil_user = Profil();
  String nature_user = "1";

  List<String> array_nature = ['Particulier', 'Professionel'];

  List<String> array_situation = ['M.', 'Mme.'];

  List<Ville> villelist = [];
  List<Ville> villelist_child = [];
  late Ville selected_ville;

  late Ville selected_ville_child;


  final ImagePicker _picker = ImagePicker();
  CkeckAuthenticate ckeckAuthenticate = Get.put(CkeckAuthenticate());


  TextEditingController code_sms_controller = TextEditingController();




  @override
  void initState() {
    get_info_profil();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: Scaffold(
        backgroundColor: ColorConstant.background_home_page,
        appBar: AppBar(
          surfaceTintColor: Colors.white,
          elevation: 3,
          shadowColor: Colors.black,
          backgroundColor: Colors.white,
          title: const Text('Profil'),
          actions: [IconButton(onPressed: () {
            showDialogLogout();
          }, icon: const Icon(Icons.logout))],
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: profil_user.id != null
                ? Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text("Données de compte"),
                const SizedBox(height: 5),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.black26,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Text("Adresse e-mail"),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text("${profil_user.email}"),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30),
                                  color: profil_user.emailConf == "1" ? Colors.green : Colors.red,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                  child: Text(
                                    profil_user.emailConf == "1" ? "verifié" : "Non vérifié",
                                    style: const TextStyle(color: Colors.white, fontSize: 13),
                                  ),
                                )),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            GestureDetector(
                              child: const Padding(
                                padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                child: Text(
                                  "MODIFER",
                                  style: TextStyle(
                                    decoration: TextDecoration.underline,
                                    decorationStyle: TextDecorationStyle.dashed,
                                  ),
                                ),
                              ),
                              onTap: () {
                                showDialogUpdateAdresseEmail();
                              },
                            ),
                          ],
                        ),
                        const Divider(color: Colors.black87),
                        const Row(
                          children: [
                            Text("Mot de passe"),
                          ],
                        ),
                        const SizedBox(height: 2),
                        const Row(
                          children: [
                            Text("*******"),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            GestureDetector(
                              child: const Padding(
                                padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                child: Text(
                                  "MODIFER",
                                  style: TextStyle(decoration: TextDecoration.underline, decorationStyle: TextDecorationStyle.dashed),
                                ),
                              ),
                              onTap: () {
                                showDialogUpdateMotdepasse();
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 5),
                const Text("Données personnelles"),
                const SizedBox(height: 5),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.black26,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Text("Nature"),
                          ],
                        ),
                        const SizedBox(height: 2),
                        profil_user.nature == "2"
                            ? Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Row(
                              children: [
                                Text("Professionel"),
                              ],
                            ),
                            Row(
                              children: [
                                Text("Raison sociale : ${profil_user.raisonSocial}"),
                              ],
                            ),
                            Row(
                              children: [
                                Text("Représentant légal : ${profil_user.representantLegal}"),
                              ],
                            ),
                            Row(
                              children: [
                                Text("Identifiant unique (RNE) : ${profil_user.identifiantUnique}"),
                              ],
                            ),
                          ],
                        )
                            : const Row(
                          children: [
                            Text("Particulier"),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            GestureDetector(
                                child: const Padding(
                                  padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                  child: Text(
                                    "MODIFER",
                                    style: TextStyle(
                                      decoration: TextDecoration.underline,
                                      decorationStyle: TextDecorationStyle.dashed,
                                    ),
                                  ),
                                ),
                                onTap: () {
                                  showDialogUpdateNature();
                                }),
                          ],
                        ),
                        const Divider(color: Colors.black87),
                        const Row(
                          children: [
                            Text("Situation"),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text(profil_user.situation == "mme" ? "Madame" : "Monsieur"),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            GestureDetector(
                                child: const Padding(
                                  padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                  child: Text(
                                    "MODIFER",
                                    style: TextStyle(
                                      decoration: TextDecoration.underline,
                                      decorationStyle: TextDecorationStyle.dashed,
                                    ),
                                  ),
                                ),
                                onTap: () {
                                  showDialogUpdateSituation();
                                }),
                          ],
                        ),
                        const Divider(color: Colors.black87),
                        const Row(
                          children: [
                            Text("Nom et prénom"),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text("${profil_user.nom}"),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30),
                                  color: profil_user.nomConf == "1" ? Colors.green : Colors.red,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                  child: Text(
                                    profil_user.nomConf == "1" ? "verifié" : "Non vérifié",
                                    style: const TextStyle(color: Colors.white, fontSize: 11),
                                  ),
                                )),
                          ],
                        ),
                        const Divider(color: Colors.black87),
                        const Row(
                          children: [
                            Text("Nom d'utilisateur"),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text("${profil_user.nomUtil}"),
                          ],
                        ),
                        const Divider(color: Colors.black87),
                        const Row(
                          children: [
                            Text("Date de naissance"),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text("${profil_user.dateNais}"),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            GestureDetector(
                                child: const Padding(
                                  padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                  child: Text(
                                    "MODIFER",
                                    style: TextStyle(decoration: TextDecoration.underline, decorationStyle: TextDecorationStyle.dashed),
                                  ),
                                ),
                                onTap: () async {
                                  final DateTime? pickedDate = await showDatePicker(
                                    context: context,
                                    initialDate: DateTime.now(),
                                    firstDate: DateTime(1950),
                                    lastDate: DateTime.now(),
                                    locale: const Locale('fr')
                                  );


                                  if (pickedDate != null) {
                                    String datePicked = pickedDate.toString().split(" ")[0];
                                    print(datePicked);
                                    String string_date_naissance = datePicked ;
                                    update_profil("Date","", "","","","","","","","","","","","","","","",string_date_naissance,"", false);
                                  }
                                }),
                          ],
                        ),
                        const Divider(color: Colors.black87),
                        const Row(
                          children: [
                            Text("Télèphone"),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text("${profil_user.numTel}"),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30),
                                  color: profil_user.telConf == "1" ? Colors.green : Colors.red,
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                  child: Text(
                                    profil_user.telConf == "1" ? "verifié" : "Non vérifié",
                                    style: const TextStyle(color: Colors.white, fontSize: 11),
                                  ),
                                )),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            GestureDetector(
                                child: const Padding(
                                  padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                  child: Text(
                                    "MODIFER",
                                    style: TextStyle(
                                      decoration: TextDecoration.underline,
                                      decorationStyle: TextDecorationStyle.dashed,
                                    ),
                                  ),
                                ),
                                onTap: () {
                                  showDialogUpdateTelephone();
                                }),
                          ],
                        ),
                        const SizedBox(height: 10),
                        profil_user.telConf == "1"
                            ? const SizedBox()
                            : Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                    flex: 75,
                                    child: Padding(
                                      padding: const EdgeInsets.only(right: 8.0),
                                      child: Container(
                                        height: 40,
                                        padding: const EdgeInsets.all(0),
                                        margin: const EdgeInsets.all(0),
                                        child: TextField(
                                          controller : code_sms_controller ,
                                          textAlign: TextAlign.center,
                                          style: const TextStyle(fontSize: 13),
                                          decoration: const InputDecoration(
                                            hintText: 'code',
                                            border: OutlineInputBorder(
                                                borderSide: BorderSide(color: Colors.black),
                                              borderRadius: BorderRadius.only(
                                                topLeft: Radius.circular(30.0),
                                                bottomLeft: Radius.circular(30.0),
                                                bottomRight: Radius.circular(30.0),
                                                topRight: Radius.circular(30.0),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    )),
                                Expanded(
                                  flex: 25,
                                  child: GestureDetector(
                                    child: Container(
                                        height: 40,
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(30),
                                          color: ColorConstant.second_color,
                                        ),
                                        alignment: Alignment.center,
                                        child: const Padding(
                                          padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                          child: Text(
                                            "VALIDER",
                                            textAlign: TextAlign.center,
                                            style: TextStyle(color: Colors.white, fontSize: 11),
                                          ),
                                        )),
                                    onTap: () {
                                      // verif tel with code sended from sms
                                      String string_edittext_code_verif_tel = code_sms_controller.text ;
                                      update_profil("valide_code_sms","", "","","","","","","","","","","","","","","","",string_edittext_code_verif_tel,false) ;
                                    },
                                  ),
                                )
                              ],
                            ),
                            const SizedBox(height: 5),
                            const Row(
                              children: [
                                Text(
                                  "Saisir le code reçu par sms pour vérifier votre numéro",
                                  style: TextStyle(color: Colors.red, fontSize: 13),
                                )
                              ],
                            ),
                            Row(
                              children: [
                                GestureDetector(
                                  child: const Text("Renvoyer le code de vérification",
                                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
                                  onTap: () {
                                    // send again sms with code verification
                                    update_profil("send_code_sms","", "","","","","","","","","","","","","","","","","",false);
                                  },
                                )
                              ],
                            ),
                          ],
                        )
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 5),
                const Text("Adresse"),
                const SizedBox(height: 5),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: Colors.black26,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Row(
                          children: [
                            Text("Adresse"),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Row(
                          children: [
                            Text("${profil_user.ville} "),
                            Text(profil_user.delegation != "" ? ", ${profil_user.delegation}" : ""),
                            Text(profil_user.adresse != "" ? ", ${profil_user.adresse}" : ""),
                            Text(profil_user.boitePostale != "" ? ", ${profil_user.boitePostale}" : ""),
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            GestureDetector(
                                child: const Padding(
                                  padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                  child: Text(
                                    "MODIFER",
                                    style: TextStyle(
                                      decoration: TextDecoration.underline,
                                      decorationStyle: TextDecorationStyle.dashed,
                                    ),
                                  ),
                                ),
                                onTap: () {
                                  villelist.clear();
                                  villelist = Ville.get_all_ville_parent();
                                  showDialogUpdateAdresse();
                                }),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 5),

                // display only user have pack

                profil_user.pack != "0"
                    ? Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text("Données de la boutique"),
                    const SizedBox(height: 5),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.black26,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Row(
                              children: [
                                Text("Logo(Format Carré)"),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                // display image boutique here
                                profil_user.logoStore != ""
                                    ? Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Image.network(
                                    '${StringConstant.base_url}media/shop/${profil_user.logoStore}',
                                    fit: BoxFit.fitHeight,
                                    height: 50,
                                    width: 50,
                                  ),
                                )
                                    : const SizedBox(),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                GestureDetector(
                                    child: Padding(
                                      padding: const EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                      child: Text(
                                        profil_user.logoStore != "" ? "SUPPRIMER" : "TÉLÉCHARGER UNE IMAGE",
                                        style: const TextStyle(
                                          decoration: TextDecoration.underline,
                                          decorationStyle: TextDecorationStyle.dashed,
                                        ),
                                      ),
                                    ),
                                    onTap: () {
                                      if (profil_user.logoStore != "") {
                                        //delete logo
                                        showDialogdeletePhoto('logo');
                                      } else {
                                        //upload logo
                                        _pickImage('logo');
                                      }
                                    }),
                              ],
                            ),
                            const Divider(color: Colors.black87),
                            const Row(
                              children: [
                                Text("Banniére (923px*280px)"),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                // display image couverture boutique here
                                profil_user.bannerStore != ""
                                    ? Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Image.network(
                                    '${StringConstant.base_url}media/shop/${profil_user.bannerStore}',
                                    fit: BoxFit.fitHeight,
                                    height: 50,
                                  ),
                                )
                                    : const SizedBox(),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                GestureDetector(
                                    child: Padding(
                                      padding: const EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                      child: Text(
                                        profil_user.bannerStore != "" ? "SUPPRIMER" : "TÉLÉCHARGER UNE IMAGE",
                                        style: const TextStyle(
                                          decoration: TextDecoration.underline,
                                          decorationStyle: TextDecorationStyle.dashed,
                                        ),
                                      ),
                                    ),
                                    onTap: () {
                                      if (profil_user.bannerStore != "") {
                                        //delete banner
                                        showDialogdeletePhoto('banner');
                                      } else {
                                        //upload banner
                                        _pickImage('banner');
                                      }
                                    }),
                              ],
                            ),
                            const Divider(color: Colors.black87),
                            const Row(
                              children: [
                                Text("Lien personnalisé"),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  width : MediaQuery.of(context).size.width * 0.85,
                                  child: Padding(
                                    padding: const EdgeInsets.only(right: 15.0),
                                    child: Text("${StringConstant.base_url}boutique/${profil_user.urlStore}",maxLines: 3,),
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                GestureDetector(
                                    child: const Padding(
                                      padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                      child: Text(
                                        "MODIFER",
                                        style: TextStyle(
                                          decoration: TextDecoration.underline,
                                          decorationStyle: TextDecorationStyle.dashed,
                                        ),
                                      ),
                                    ),
                                    onTap: () {
                                      showDialogUpdatePersonalLink();
                                    }),
                              ],
                            ),
                            const Divider(color: Colors.black87),
                            const Row(
                              children: [
                                Text("Lien Google Map"),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                Text("${profil_user.gps}"),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                GestureDetector(
                                    child: const Padding(
                                      padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                      child: Text(
                                        "MODIFER",
                                        style: TextStyle(
                                          decoration: TextDecoration.underline,
                                          decorationStyle: TextDecorationStyle.dashed,
                                        ),
                                      ),
                                    ),
                                    onTap: () {
                                      showDialogUpdateGoogleMapLink();
                                    }),
                              ],
                            ),
                            const Divider(color: Colors.black87),
                            const Row(
                              children: [
                                Text("Messenger et Whatsapp"),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                Text("Messenger : ${profil_user.messenger}"),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                Text("Whatsapp : ${profil_user.whatsapp}"),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                GestureDetector(
                                    child: const Padding(
                                      padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                      child: Text(
                                        "MODIFER",
                                        style: TextStyle(
                                          decoration: TextDecoration.underline,
                                          decorationStyle: TextDecorationStyle.dashed,
                                        ),
                                      ),
                                    ),
                                    onTap: () {
                                      showDialogUpdateMessengerWhatsapp();
                                    }),
                              ],
                            ),
                            const Divider(color: Colors.black87),
                            const Row(
                              children: [
                                Text("Facebook , Instagram et Tiktok"),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                Text("Website : ${profil_user.website}"),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                Text("Facebook : ${profil_user.facebook}"),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                Text("Instagram : ${profil_user.instagram}"),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                Text("Tiktok : ${profil_user.tiktok}"),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                GestureDetector(
                                    child: const Padding(
                                      padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                      child: Text(
                                        "MODIFER",
                                        style: TextStyle(
                                          decoration: TextDecoration.underline,
                                          decorationStyle: TextDecorationStyle.dashed,
                                        ),
                                      ),
                                    ),
                                    onTap: () {
                                      showDialogUpdateWebsiteFacebookInstagramTiktok();
                                    }),
                              ],
                            ),
                            const Divider(color: Colors.black87),
                            const Row(
                              children: [
                                Text("Description"),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Row(
                              children: [
                                Text(profil_user.description != "" ? "${profil_user.description}" : "Bienvenu à noptre boutique"),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                GestureDetector(
                                  child: const Padding(
                                    padding: EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                                    child: Text(
                                      "MODIFER",
                                      style: TextStyle(
                                        decoration: TextDecoration.underline,
                                        decorationStyle: TextDecorationStyle.dashed,
                                      ),
                                    ),
                                  ),
                                  onTap: () {
                                    showDialogUpdateDescription();
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                )
                    : const SizedBox(height: 5),
              ],
            )
                : const BuildInterfaceProfilShimmer(),
          ),
        ),
      ),
    );
  }

  /// dialog update email adress
  void showDialogUpdateAdresseEmail() {

    TextEditingController update_email_password_controller = TextEditingController();
    TextEditingController update_email_email_controller = TextEditingController();


    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
          contentPadding: const EdgeInsets.only(top: 10.0),
          title: const Text(
            'Addresse e-mail',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          content: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 40,
                        padding: const EdgeInsets.all(0),
                        margin: const EdgeInsets.all(0),
                        child:  TextField(
                          controller : update_email_password_controller,
                          textAlign: TextAlign.start,
                          textAlignVertical: TextAlignVertical.center,
                          style: const TextStyle(fontSize: 13),
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                            hintText: 'Votre nouvelle adresse e-mail ',
                            border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(30.0),
                                bottomLeft: Radius.circular(30.0),
                                bottomRight: Radius.circular(30.0),
                                topRight: Radius.circular(30.0),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 5),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 40,
                        padding: const EdgeInsets.all(0),
                        margin: const EdgeInsets.all(0),
                        child: TextField(
                          controller: update_email_email_controller,
                          textAlign: TextAlign.start,
                          style: const TextStyle(fontSize: 13),
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                            hintText: 'Saisir votre mot de passe ',
                            border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(30.0),
                                bottomLeft: Radius.circular(30.0),
                                bottomRight: Radius.circular(30.0),
                                topRight: Radius.circular(30.0),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // Implement logic to update email
                String string_update_email = update_email_email_controller.text ;
                String string_password_update = update_email_password_controller.text ;
                update_profil("AdresseMail",string_update_email, string_password_update,"","","","","","","","","","","","","","","","",true);
              },
              child: const Text('Update'),
            ),
          ],
        );
      },
    );
  }

  /// dialog update password
  void showDialogUpdateMotdepasse() {

    TextEditingController update_password_ancien_controller = TextEditingController() ;
    TextEditingController update_password_new_controller = TextEditingController() ;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
          contentPadding: const EdgeInsets.only(top: 10.0),
          title: const Text(
            'Mot de passe',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          content: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 40,
                        padding: const EdgeInsets.all(0),
                        margin: const EdgeInsets.all(0),
                        child:  TextField(
                          controller: update_password_ancien_controller,
                          textAlign: TextAlign.start,
                          style: const TextStyle(fontSize: 13),
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                            hintText: 'Mot de passe actuel ',
                            border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(30.0),
                                bottomLeft: Radius.circular(30.0),
                                bottomRight: Radius.circular(30.0),
                                topRight: Radius.circular(30.0),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 5),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 40,
                        padding: const EdgeInsets.all(0),
                        margin: const EdgeInsets.all(0),
                        child: TextField(
                          controller: update_password_new_controller,
                          textAlign: TextAlign.start,
                          style: const TextStyle(fontSize: 13),
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                            hintText: 'Nouveau mot de passe',
                            border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(30.0),
                                bottomLeft: Radius.circular(30.0),
                                bottomRight: Radius.circular(30.0),
                                topRight: Radius.circular(30.0),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // Implement logic to update email
                String string_ancien_password = update_password_ancien_controller.text ;
                String string_new_password = update_password_new_controller.text ;
                update_profil("MotDePasse","", "",string_ancien_password,string_new_password,"","","","","","","","","","","","","","",true);
              },
              child: const Text('Update'),
            ),
          ],
        );
      },
    );
  }

  /// dialog update nature
  showDialogUpdateNature() {
    nature_user = profil_user.nature!;


    TextEditingController update_nature_raison_social_controller = TextEditingController(text: profil_user.raisonSocial) ;
    TextEditingController update_nature_representant_legal_controller = TextEditingController(text: profil_user.representantLegal) ;
    TextEditingController update_nature_identifiant_unique_controller = TextEditingController(text: profil_user.identifiantUnique) ;

    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Nature',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            alignment: Alignment.center,
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: DropdownMenu<String>(
                              initialSelection: nature_user == "0" ? "Particulier" : "Professionel",
                              requestFocusOnTap: false,
                              onSelected: (String? value) {
                                // This is called when the user selects an item.
                                setState(() {
                                  if (value == "Particulier") {
                                    nature_user = "0";
                                  } else {
                                    nature_user = "2";
                                  }
                                });
                              },

                              inputDecorationTheme: const InputDecorationTheme(
                                contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                border: OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.black),
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(30.0),
                                  bottomLeft: Radius.circular(30.0),
                                  bottomRight: Radius.circular(30.0),
                                  topRight: Radius.circular(30.0),
                                ),
                              ),),
                              expandedInsets: const EdgeInsets.only(left: 0.0, right: 0.0),
                              dropdownMenuEntries: array_nature.map<DropdownMenuEntry<String>>((String value) {
                                return DropdownMenuEntry<String>(
                                  value: value,
                                  label: value,
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    nature_user == "0" ? const SizedBox()
                        : Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                height: 40,
                                padding: const EdgeInsets.all(0),
                                margin: const EdgeInsets.all(0),
                                child:  TextField(
                                  controller: update_nature_raison_social_controller,
                                  textAlign: TextAlign.start,
                                  style: const TextStyle(fontSize: 13),
                                  decoration: const InputDecoration(
                                    contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                    hintText: 'Raison sociale',
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide(color: Colors.black),
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(30.0),
                                        bottomLeft: Radius.circular(30.0),
                                        bottomRight: Radius.circular(30.0),
                                        topRight: Radius.circular(30.0),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 5),
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                height: 40,
                                padding: const EdgeInsets.all(0),
                                margin: const EdgeInsets.all(0),
                                child:  TextField(
                                  controller: update_nature_representant_legal_controller,
                                  textAlign: TextAlign.start,
                                  style: const TextStyle(fontSize: 13),
                                  decoration: const InputDecoration(
                                    contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                    hintText: 'Représentant légal',
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide(color: Colors.black),
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(30.0),
                                        bottomLeft: Radius.circular(30.0),
                                        bottomRight: Radius.circular(30.0),
                                        topRight: Radius.circular(30.0),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 5),
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                height: 40,
                                padding: const EdgeInsets.all(0),
                                margin: const EdgeInsets.all(0),
                                child:  TextField(
                                  controller : update_nature_identifiant_unique_controller,
                                  textAlign: TextAlign.start,
                                  style: const TextStyle(fontSize: 13),
                                  decoration: const InputDecoration(
                                    contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                    hintText: 'Identifiant unique (RNE)',
                                    border: OutlineInputBorder(
                                      borderSide: BorderSide(color: Colors.black),
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(30.0),
                                        bottomLeft: Radius.circular(30.0),
                                        bottomRight: Radius.circular(30.0),
                                        topRight: Radius.circular(30.0),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Implement logic to update email
                    String string_spinner_nature = nature_user;
                    String string_raison_social_dialog = update_nature_raison_social_controller.text ;
                    String string_representant_legal_dialog = update_nature_representant_legal_controller.text ;
                    String string_identifiant_unique_dialog = update_nature_identifiant_unique_controller.text ;
                    update_profil("nature","", "","","","",string_spinner_nature,string_raison_social_dialog,string_representant_legal_dialog,string_identifiant_unique_dialog,"","","","","","","","","",true);
                  },
                  child: const Text('Update'),
                ),
              ],
            );
          });
        });
  }


  /// dialog update situatiuon
  showDialogUpdateSituation() {

    String Situation = "M." ;
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Situation',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            alignment: Alignment.center,
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: DropdownMenu<String>(
                              initialSelection: array_situation == "mme" ? "Mme." : "M.",
                              requestFocusOnTap: false,

                              onSelected: (String? value) {
                                // This is called when the user selects an item.
                                if(value == "M."){
                                  Situation  = "m";
                                }else{
                                  Situation = "mme";
                                }
                                setState(() {

                                });
                              },
                              inputDecorationTheme: const InputDecorationTheme(
                                contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                border: OutlineInputBorder(
                                borderSide: BorderSide(color: Colors.black),
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(30.0),
                                  bottomLeft: Radius.circular(30.0),
                                  bottomRight: Radius.circular(30.0),
                                  topRight: Radius.circular(30.0),
                                ),
                              ),),
                              expandedInsets: const EdgeInsets.only(left: 0.0, right: 0.0),
                              dropdownMenuEntries: array_situation.map<DropdownMenuEntry<String>>((String value) {
                                return DropdownMenuEntry<String>(
                                  value: value,
                                  label: value,
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Implement logic situation
                    update_profil("Situation","", "","","",Situation,"","","","","","","","","","","","","" ,true);
                  },
                  child: const Text('Update'),
                ),
              ],
            );
          });
        });
  }


  /// dialog update phone number
  void showDialogUpdateTelephone() {
    TextEditingController update_telephone_controller = TextEditingController() ;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
          contentPadding: const EdgeInsets.only(top: 10.0),
          title: const Text(
            'Téléphone',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16),
          ),
          content: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 40,
                        padding: const EdgeInsets.all(0),
                        margin: const EdgeInsets.all(0),
                        child: TextField(
                          controller : update_telephone_controller ,
                          textAlign: TextAlign.start,
                          style: const TextStyle(fontSize: 13),
                          decoration: const InputDecoration(
                            contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                            hintText: 'Nouveau numéro de téléphone',
                            border: OutlineInputBorder(
                              borderSide: BorderSide(color: Colors.black),
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(30.0),
                                bottomLeft: Radius.circular(30.0),
                                bottomRight: Radius.circular(30.0),
                                topRight: Radius.circular(30.0),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 5),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                // Implement logic to update email
                String string_telephone_dialog = update_telephone_controller.text ;
                update_profil("NumeroTelephone","", "","","","","","","","","","",string_telephone_dialog,"","","","","","",true);
              },
              child: const Text('Update'),
            ),
          ],
        );
      },
    );
  }


  /// dialog update adresse
  showDialogUpdateAdresse() {
    villelist.insert(0, Ville(id: '0', nom: '--Ville--', idParent: '0'));
    selected_ville =  villelist.first;
    villelist_child.add(Ville(id: "0", nom: "--Gouvernorate--", idParent: "0"));
    selected_ville_child = villelist_child.first;
    String villeselected_tosend = "" ;
    String gouvernoratselected_tosend = "" ;

    TextEditingController _controller_adresse = TextEditingController(text: profil_user.adresse);
    TextEditingController _controller_codepostal = TextEditingController(text: profil_user.boitePostale);

    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Adresse',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            alignment: Alignment.center,
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: DropdownMenu<Ville>(
                              initialSelection: selected_ville,
                              inputDecorationTheme: const InputDecorationTheme(
                                  contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                  border:  OutlineInputBorder(
                                    borderSide: BorderSide(color: Colors.black),
                                    borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(30.0),
                                      bottomLeft: Radius.circular(30.0),
                                      bottomRight: Radius.circular(30.0),
                                      topRight: Radius.circular(30.0),
                                    ),
                                  )
                              ),
                              requestFocusOnTap: false,
                              onSelected: (Ville? value) {
                                // This is called when the user selects an item.
                                setState(() {
                                  if (value!.id == "0") {
                                    villelist_child.clear();
                                    villelist_child.add(Ville(id: "0", nom: "--Gouvernorate--", idParent: "0"));
                                    villeselected_tosend = "" ;
                                    gouvernoratselected_tosend = "" ;
                                  } else {
                                    List<Ville> sous_list = Ville.get_all_ville_child(value.id!);
                                    sous_list.removeAt(0);
                                    villelist_child = sous_list;
                                    villeselected_tosend = value.nom! ;
                                    gouvernoratselected_tosend = "" ;
                                  }
                                  selected_ville = value;
                                });
                              },
                              expandedInsets: const EdgeInsets.all(2.0),
                              dropdownMenuEntries: villelist.map<DropdownMenuEntry<Ville>>((Ville value) {
                                return DropdownMenuEntry<Ville>(
                                  value: value,
                                  label: value.nom!,
                                );
                              }).toList(),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                              alignment: Alignment.center,
                              height: 40,
                              padding: const EdgeInsets.all(0),
                              margin: const EdgeInsets.all(0),
                              child: DropdownMenu<Ville>(
                                initialSelection: selected_ville_child,
                                inputDecorationTheme: const InputDecorationTheme(
                                    contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                    border:  OutlineInputBorder(
                                      borderSide: BorderSide(color: Colors.black),
                                      borderRadius: BorderRadius.only(
                                        topLeft: Radius.circular(30.0),
                                        bottomLeft: Radius.circular(30.0),
                                        bottomRight: Radius.circular(30.0),
                                        topRight: Radius.circular(30.0),
                                      ),
                                    )
                                ),
                                requestFocusOnTap: false,
                                onSelected: (Ville? value) {
                                  // This is called when the user selects an item.
                                  setState(() {
                                    selected_ville_child = value!;
                                    if(value.id != "0"){
                                      gouvernoratselected_tosend = value.nom! ;
                                    }

                                  });
                                },
                                expandedInsets: const EdgeInsets.all(2.0),
                                dropdownMenuEntries: villelist_child.map<DropdownMenuEntry<Ville>>((Ville value) {
                                  return DropdownMenuEntry<Ville>(
                                    value: value,
                                    label: value.nom!,
                                  );
                                }).toList(),
                              )
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: TextField(
                              controller: _controller_adresse,
                              textAlign: TextAlign.start,
                              style: const TextStyle(fontSize: 13),
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                hintText: "Complément d'adresse",
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.black),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30.0),
                                    bottomLeft: Radius.circular(30.0),
                                    bottomRight: Radius.circular(30.0),
                                    topRight: Radius.circular(30.0),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: TextField(
                              controller: _controller_codepostal,
                              textAlign: TextAlign.start,
                              style: const TextStyle(fontSize: 13),
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                hintText: "Code postal",
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.black),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30.0),
                                    bottomLeft: Radius.circular(30.0),
                                    bottomRight: Radius.circular(30.0),
                                    topRight: Radius.circular(30.0),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Implement logic to update adresse
                    String string_addresse_complete_dialog = _controller_adresse.text ;
                    String string_code_postal_dialog = _controller_codepostal.text ;
                    String ville = villeselected_tosend ;
                    String gouvernorate = gouvernoratselected_tosend ;

                    update_profil("BlockAdresse","", "","","","","","","","","","","",ville,gouvernorate,string_addresse_complete_dialog,string_code_postal_dialog,"","",true);
                  },
                  child: const Text('Update'),
                ),
              ],
            );
          });
        });
  }


  /// dialog update personnal link
  showDialogUpdatePersonalLink() {
    TextEditingController _controller_personnal_link = TextEditingController(text: profil_user.urlStore);

    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Lien personnalisé',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(left: 8.0, right: 5),
                            child: Text('${StringConstant.base_url}boutique/'),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: TextField(
                              controller: _controller_personnal_link,
                              textAlign: TextAlign.start,
                              style: const TextStyle(fontSize: 13),
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                hintText: "Nom personnalisé",
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.black),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30.0),
                                    bottomLeft: Radius.circular(30.0),
                                    bottomRight: Radius.circular(30.0),
                                    topRight: Radius.circular(30.0),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Implement logic to update personal link
                    String string_update_nom_store = _controller_personnal_link.text ;
                    if(string_update_nom_store != "") {
                      update_personal_link(string_update_nom_store);
                    }
                  },
                  child: const Text('Update'),
                ),
              ],
            );
          });
        });
  }


  /// dialog update google map link
  showDialogUpdateGoogleMapLink() {
    TextEditingController _controller_google_map_link = TextEditingController(text: profil_user.gps);

    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Lien Google Map',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [

                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: TextField(
                              controller: _controller_google_map_link,
                              textAlign: TextAlign.start,
                              style: const TextStyle(fontSize: 13),
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                hintText: "Lien google map",
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.black),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30.0),
                                    bottomLeft: Radius.circular(30.0),
                                    bottomRight: Radius.circular(30.0),
                                    topRight: Radius.circular(30.0),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Implement logic to update google map link
                    String string_update_link_map = _controller_google_map_link.text;
                    update_google_map_link(string_update_link_map);
                  },
                  child: const Text('Update'),
                ),
              ],
            );
          });
        });
  }

  /// dialog update messenger and whatsapp
  showDialogUpdateMessengerWhatsapp() {
    TextEditingController _controller_messenger = TextEditingController(text: profil_user.messenger);
    TextEditingController _controller_whatsapp = TextEditingController(text: profil_user.whatsapp);

    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Messenger et Whatsapp',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [

                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: TextField(
                              controller: _controller_messenger,
                              textAlign: TextAlign.start,
                              style: const TextStyle(fontSize: 13),
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                hintText: "Messenger",
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.black),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30.0),
                                    bottomLeft: Radius.circular(30.0),
                                    bottomRight: Radius.circular(30.0),
                                    topRight: Radius.circular(30.0),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: TextField(
                              controller: _controller_whatsapp,
                              textAlign: TextAlign.start,
                              style: const TextStyle(fontSize: 13),
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                hintText: "Whatsapp",
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.black),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30.0),
                                    bottomLeft: Radius.circular(30.0),
                                    bottomRight: Radius.circular(30.0),
                                    topRight: Radius.circular(30.0),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Implement logic to update messenger and whatsapp
                    String string_update_messenger = _controller_messenger.text ;
                    String string_update_whatsapp = _controller_whatsapp.text ;
                    update_messenger_and_whatsapp(string_update_messenger,string_update_whatsapp);

                  },
                  child: const Text('Update'),
                ),
              ],
            );
          });
        });
  }


  /// dialog update Facebook , instagram ,tiktok , website
  showDialogUpdateWebsiteFacebookInstagramTiktok() {
    TextEditingController _controller_facebook = TextEditingController(text: profil_user.facebook);
    TextEditingController _controller_instagram = TextEditingController(text: profil_user.instagram);
    TextEditingController _controller_tiktok = TextEditingController(text: profil_user.tiktok);
    TextEditingController _controller_website = TextEditingController(text: profil_user.website);

    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Facebook , Instagram , Tiktok , Website',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [

                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: TextField(
                              controller: _controller_website,
                              textAlign: TextAlign.start,
                              style: const TextStyle(fontSize: 13),
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                hintText: "Website",
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.black),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30.0),
                                    bottomLeft: Radius.circular(30.0),
                                    bottomRight: Radius.circular(30.0),
                                    topRight: Radius.circular(30.0),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: TextField(
                              controller: _controller_facebook,
                              textAlign: TextAlign.start,
                              style: const TextStyle(fontSize: 13),
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                hintText: "Facebook",
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.black),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30.0),
                                    bottomLeft: Radius.circular(30.0),
                                    bottomRight: Radius.circular(30.0),
                                    topRight: Radius.circular(30.0),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: TextField(
                              controller: _controller_instagram,
                              textAlign: TextAlign.start,
                              style: const TextStyle(fontSize: 13),
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                hintText: "Instagram",
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.black),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30.0),
                                    bottomLeft: Radius.circular(30.0),
                                    bottomRight: Radius.circular(30.0),
                                    topRight: Radius.circular(30.0),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 40,
                            padding: const EdgeInsets.all(0),
                            margin: const EdgeInsets.all(0),
                            child: TextField(
                              controller: _controller_tiktok,
                              textAlign: TextAlign.start,
                              style: const TextStyle(fontSize: 13),
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.symmetric(vertical: 0 , horizontal : 10),
                                hintText: "Tiktok",
                                border: OutlineInputBorder(
                                  borderSide: BorderSide(color: Colors.black),
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(30.0),
                                    bottomLeft: Radius.circular(30.0),
                                    bottomRight: Radius.circular(30.0),
                                    topRight: Radius.circular(30.0),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Implement logic to update facebook, intagram, tiktok ad website
                    String string_update_facebook = _controller_facebook.text ;
                    String string_update_instagram =  _controller_instagram.text ;
                    String string_update_tiktok =_controller_tiktok.text ;
                    String string_update_website = _controller_website.text ;
                    update_website_facebook_instagram_tiktok(string_update_website,string_update_facebook,string_update_instagram,string_update_tiktok) ;
                  },
                  child: const Text('Update'),
                ),
              ],
            );
          });
        });
  }

  /// dialog update description
  showDialogUpdateDescription() {
    TextEditingController _controller_description = TextEditingController(text: profil_user.description);

    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Description',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.only(left: 0, right: 0, top: 10),
                            margin: const EdgeInsets.all(0),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              shape: BoxShape.rectangle,
                              border: Border.all(
                                color: Colors.black,
                                width: 1.0,
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.only(left: 8.0, right: 5),
                              child: TextField(
                                maxLines: 4,
                                controller: _controller_description,
                                textAlign: TextAlign.start,
                                style: const TextStyle(fontSize: 11),
                                textAlignVertical: TextAlignVertical.center,
                                decoration: const InputDecoration(
                                  hintText: "Description",
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.only(bottom: 18.0),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    // Implement logic to update description
                    String string_update_description = _controller_description.text ;
                    update_description(string_update_description);
                  },
                  child: const Text('Update'),
                ),
              ],
            );
          });
        });
  }

  showDialogdeletePhoto(String type) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: Text(
                type == "logo" ? 'Logo' : "Bannière",
                textAlign: TextAlign.center,
                style: const TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Center(
                              child: Text(type == "logo" ? "Êtes-vous sûr de supprimer le logo" : "Êtes-vous sûr de supprimer la bannière")),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Annuler'),
                ),
                TextButton(
                  onPressed: () {
                    // Implement logic to remove photo
                    remove_photo(type);
                    Navigator.of(context).pop();
                  },
                  child: const Text('Valider'),
                ),
              ],
            );
          });
        });
  }

  /// code upload photo
  Future<void> _pickImage(String type) async {
    final XFile? image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      File imageFile = File(image.path);
      await _uploadImage(imageFile, type);
    }
  }

  Future<void> _uploadImage(File imageFile, String type) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    try {
      var stream = http.ByteStream(imageFile.openRead());
      var length = await imageFile.length();
      var request = http.MultipartRequest('POST', Uri.parse('${StringConstant.base_url}api/upload_photo.php'));
      request.files.add(
        http.MultipartFile(
          type,
          stream,
          length,
          filename: 'image.jpg',
          contentType: MediaType('image', '*'),
        ),
      );
      request.fields['token'] = token!;
      var response = await request.send();
      if (response.statusCode == 200) {
        final responseString = await response.stream.bytesToString();
        ResponseRequest _ResponseRequest = ResponseRequest.fromJson(json.decode(responseString));
        print(_ResponseRequest.toJson().toString());
        print('Image uploaded successfully');
        get_info_profil();
      } else {
        print('Image upload failed');
      }
    } catch (e) {
      print('Error: $e');
    }
  }

  /// remove picture
  void remove_photo(String type) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/remove_photo_user.php?token=${token}&del=${type}'));

    if (res.statusCode == 200) {
      ResponseRequest _response_request = ResponseRequest.fromJson(json.decode(res.body));
      if (_response_request.etat == "1") {
        get_info_profil();
      }
    }
  }
  /// get info profile user
  void get_info_profil() async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_profil.php?token=${token}'));
    if (res.statusCode == 200) {
      Profil _profil = Profil.fromJson(json.decode(res.body));
      setState(() {
        profil_user = _profil;
      });
    }
  }
  /// update personal link
  void update_personal_link(String string_update_nom_store ) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/update_personal_link.php?token=${token}&url=${string_update_nom_store}'));
    if (res.statusCode == 200) {
      ResponseRequest _response_request = ResponseRequest.fromJson(json.decode(res.body));
      if (_response_request.etat == "1") {
        get_info_profil();
      }
    }
  }
  /// update google map link
  void update_google_map_link(String string_google_map_link ) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/update_gps.php?token=${token}&gps=${string_google_map_link}'));
    if (res.statusCode == 200) {
      ResponseRequest _response_request = ResponseRequest.fromJson(json.decode(res.body));
      if (_response_request.etat == "1") {
        get_info_profil();
      }
    }
  }

  /// update google map link
  void update_messenger_and_whatsapp( String string_lien_messenger,String string_lien_whatsapp) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/update_messenger_and_whatsapp.php?token=${token}&messenger=${string_lien_messenger}&whatsapp=${string_lien_whatsapp}'));
    if (res.statusCode == 200) {
      ResponseRequest _response_request = ResponseRequest.fromJson(json.decode(res.body));
      if (_response_request.etat == "1") {
        get_info_profil();
      }
    }
  }

  /// update website , facebook , instagram and tiktok
  void update_website_facebook_instagram_tiktok( String string_update_website,String string_update_facebook,String string_update_instagram,String string_update_tiktok) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/update_website_facebook_instagram_tiktok.php?token=${token}&website=${string_update_website}&facebook=${string_update_facebook}&instagram=${string_update_instagram}&tiktok=${string_update_tiktok}'));
    if (res.statusCode == 200) {
      ResponseRequest _response_request = ResponseRequest.fromJson(json.decode(res.body));
      if (_response_request.etat == "1") {
        get_info_profil();
      }
    }
  }

  /// update description
  void update_description( String string_update_description) async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/update_description.php?token=${token}&description=${string_update_description}'));
    if (res.statusCode == 200) {
      ResponseRequest _response_request = ResponseRequest.fromJson(json.decode(res.body));
      if (_response_request.etat == "1") {
        get_info_profil();
      }
    }
  }


  update_profil(String action , String val_mail , String val_passe , String val_passe_actuel , String val_passe_nouveau , String val_profile_situation , String val_profile_nature , String val_raison_social , String val_representant_legal , String val_identifiant_unique , String val_Profile_nom_util , String val_Profile_nom , String val_Profile_num_tel , String val_Adresse1 , String val_delegu_2 , String val_complement_rue , String val_boite_postale_rue , String val_Profile_date ,String send_code_sms ,bool isdialog) async {
    if(isdialog){
      Navigator.of(context).pop();
    }
    ShowCustomLoaderDialog().showCustomDialog(context);
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res ;
    String _url = '${StringConstant.base_url}api/update_profil.php';


    switch (action) {
      case "AdresseMail":
        res = await http.post(Uri.parse(_url),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: {
              'token': token,
              'action': action,
              'val_mail': val_mail,
              'val_passe': val_passe,
            });
      case "MotDePasse":
        res = await http.post(Uri.parse(_url),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: {
              'token': token,
              'action': action,
              'val_passe_actuel': val_passe_actuel,
              'val_passe_nouveau': val_passe_nouveau,
            });
      case "Situation":
        res = await http.post(Uri.parse(_url),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: {
              'token': token,
              'action': action,
              'val_profile_situation': val_profile_situation ,
            });

      case "nature":
        res = await http.post(Uri.parse(_url),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: {
              'token': token,
              'action': action,
              'val_profile_nature': val_profile_nature,
              'val_raison_social': val_raison_social,
              'val_representant_legal': val_representant_legal,
              'val_identifiant_unique': val_identifiant_unique,
            });
      case "Nom":
        res = await http.post(Uri.parse(_url),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: {
              'token': token,
              'action': action,
              'val_Profile_nom': val_Profile_nom,
            });
      case "send_code_sms":
        res = await http.post(Uri.parse(_url),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: {
              'token': token,
              'action': action,
            });
      case "valide_code_sms":
        res = await http.post(Uri.parse(_url),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: {
              'token': token,
              'action': action,
              'send_code_sms': send_code_sms,
            });
      case "Date":
        res = await http.post(Uri.parse(_url),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: {
              'token': token,
              'action': action,
              'val_Profile_date': val_Profile_date,
            });
      case "NumeroTelephone":
        res = await http.post(Uri.parse(_url),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: {
              'token': token,
              'action': action,
              'val_Profile_num_tel': val_Profile_num_tel,
            });

      case "BlockAdresse":
        res = await http.post(Uri.parse(_url),
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: {
              'token': token,
              'action': action,
              'val_Adresse1': val_Adresse1,
              'val_delegu_2': val_delegu_2,
              'val_complement_rue': val_complement_rue,
              'val_boite_postale_rue': val_boite_postale_rue,
            });
      default:
        return null ;
    }
    get_info_profil();
    await Future.delayed(const Duration(seconds: 1));
    Navigator.of(context).pop();
  }

  /// logout action
  void showDialogLogout(){
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                "Se déconnecter",
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: const Padding(
                padding: EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        Expanded(
                            child: Center(
                                child: Text("Êtes-vous sûr de vouloir vous déconnecter ?")
                            )
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Annuler'),
                ),
                TextButton(
                  onPressed: () {
                    // Implement logic logout
                    _prefs.then((value){
                      ckeckAuthenticate.loggedin = false.obs ;
                      WishlistStoreSharedprefrence().remove_all_WishlistStoreSharedprefrence_data();
                      WishlistProductSharedprefrence().remove_all_WishlistProductSharedprefrence_data();
                      ListSearchSharedprefrence().remove_all_ListSearchSharedprefrence_data();
                      value.clear();
                      Navigator.of(context).pop();
                      Navigator.of(context).pop();
                    }) ;
                  },
                  child: const Text('Déconnexion'),
                ),
              ],
            );
          });
        });
  }
}
