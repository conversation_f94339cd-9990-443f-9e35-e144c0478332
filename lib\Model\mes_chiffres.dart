class MesChiffres {
  String? nbrVisitePerStore;
  String? totalVisiteForAllProduct;

  MesChiffres({this.nbrVisitePerStore, this.totalVisiteForAllProduct});

  MesChiffres.fromJson(Map<String, dynamic> json) {
    nbrVisitePerStore = json['nbr_visite_per_store'];
    totalVisiteForAllProduct = json['total_visite_for_all_product'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['nbr_visite_per_store'] = this.nbrVisitePerStore;
    data['total_visite_for_all_product'] = this.totalVisiteForAllProduct;
    return data;
  }
}
