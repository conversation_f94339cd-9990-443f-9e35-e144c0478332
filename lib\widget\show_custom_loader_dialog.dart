import 'package:flutter/material.dart';
/**
 * Created by Aymen 01/09/2023
 * to show ||| ShowCustomLoaderDialog().showCustomDialog(context);
 * to hide ||| Navigator.of(context).pop();
 */

class ShowCustomLoaderDialog{
  ShowCustomLoaderDialog();
  void showCustomDialog(BuildContext context) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: Duration(milliseconds: 10),
      pageBuilder: (_, __, ___) {
        return Center(
          child: Image.asset("./assets/images/loader1.gif",height: 50,width: 50,)
        );
      },
      transitionBuilder: (_, anim, __, child) {
        Tween<Offset> tween;
        if (anim.status == AnimationStatus.reverse) {
          tween = Tween(begin: Offset(-1, 0), end: Offset.zero);
        } else {
          tween = Tween(begin: Offset(1, 0), end: Offset.zero);
        }

        return SlideTransition(
          position: tween.animate(anim),
          child: FadeTransition(
            opacity: anim,
            child: child,
          ),
        );
      },
    );
  }
}


