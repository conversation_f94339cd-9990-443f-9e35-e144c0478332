class ResponseLogin {
  String? etat;
  String? token;
  String? iduser;

  ResponseLogin({this.etat, this.token, this.iduser});

  ResponseLogin.fromJson(Map<String, dynamic> json) {
    etat = json['etat'];
    token = json['token'];
    iduser = json['iduser'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['etat'] = this.etat;
    data['token'] = this.token;
    data['iduser'] = this.iduser;
    return data;
  }
}
