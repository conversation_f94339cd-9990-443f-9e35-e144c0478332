import 'dart:convert';

class Ville {
  String? id;
  String? nom;
  String? idParent;

  Ville({this.id, this.nom, this.idParent});

  <PERSON>.from<PERSON><PERSON>(Map<String, dynamic> json) {
    id = json['id'];
    nom = json['nom'];
    idParent = json['id_parent'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['nom'] = this.nom;
    data['id_parent'] = this.idParent;
    return data;
  }

  static String ville_list_json = "[{\"id\":\"1\",\"nom\":\"Ariana\",\"id_parent\":\"0\"},{\"id\":\"2\",\"nom\":\"ArianaVille\",\"id_parent\":\"1\"},{\"id\":\"3\",\"nom\":\"Ettadhamen\",\"id_parent\":\"1\"},{\"id\":\"4\",\"nom\":\"KalaatLandlous\",\"id_parent\":\"1\"},{\"id\":\"5\",\"nom\":\"LaSoukra\",\"id_parent\":\"1\"},{\"id\":\"6\",\"nom\":\"Mnihla\",\"id_parent\":\"1\"},{\"id\":\"7\",\"nom\":\"Raoued\",\"id_parent\":\"1\"},{\"id\":\"8\",\"nom\":\"SidiThabet\",\"id_parent\":\"1\"},{\"id\":\"9\",\"nom\":\"B\\u00e9ja\",\"id_parent\":\"0\"},{\"id\":\"10\",\"nom\":\"Amdoun\",\"id_parent\":\"9\"},{\"id\":\"11\",\"nom\":\"BejaNord\",\"id_parent\":\"9\"},{\"id\":\"12\",\"nom\":\"BejaSud\",\"id_parent\":\"9\"},{\"id\":\"13\",\"nom\":\"Goubellat\",\"id_parent\":\"9\"},{\"id\":\"14\",\"nom\":\"MejezElBab\",\"id_parent\":\"9\"},{\"id\":\"15\",\"nom\":\"Nefza\",\"id_parent\":\"9\"},{\"id\":\"16\",\"nom\":\"Teboursouk\",\"id_parent\":\"9\"},{\"id\":\"17\",\"nom\":\"Testour\",\"id_parent\":\"9\"},{\"id\":\"18\",\"nom\":\"Thibar\",\"id_parent\":\"9\"},{\"id\":\"19\",\"nom\":\"BenArous\",\"id_parent\":\"0\"},{\"id\":\"20\",\"nom\":\"BenArous\",\"id_parent\":\"19\"},{\"id\":\"21\",\"nom\":\"BouMhelElBassatine\",\"id_parent\":\"19\"},{\"id\":\"22\",\"nom\":\"ElMourouj\",\"id_parent\":\"19\"},{\"id\":\"23\",\"nom\":\"Ezzahra\",\"id_parent\":\"19\"},{\"id\":\"24\",\"nom\":\"Fouchana\",\"id_parent\":\"19\"},{\"id\":\"25\",\"nom\":\"HammamChatt\",\"id_parent\":\"19\"},{\"id\":\"26\",\"nom\":\"HammamLif\",\"id_parent\":\"19\"},{\"id\":\"27\",\"nom\":\"Megrine\",\"id_parent\":\"19\"},{\"id\":\"28\",\"nom\":\"Mohamadia\",\"id_parent\":\"19\"},{\"id\":\"29\",\"nom\":\"Mornag\",\"id_parent\":\"19\"},{\"id\":\"30\",\"nom\":\"NouvelleMedina\",\"id_parent\":\"19\"},{\"id\":\"31\",\"nom\":\"Rades\",\"id_parent\":\"19\"},{\"id\":\"32\",\"nom\":\"Bizerte\",\"id_parent\":\"0\"},{\"id\":\"33\",\"nom\":\"BizerteNord\",\"id_parent\":\"32\"},{\"id\":\"34\",\"nom\":\"BizerteSud\",\"id_parent\":\"32\"},{\"id\":\"35\",\"nom\":\"ElAlia\",\"id_parent\":\"32\"},{\"id\":\"36\",\"nom\":\"GharElMelh\",\"id_parent\":\"32\"},{\"id\":\"37\",\"nom\":\"Ghezala\",\"id_parent\":\"32\"},{\"id\":\"38\",\"nom\":\"Jarzouna\",\"id_parent\":\"32\"},{\"id\":\"39\",\"nom\":\"Joumine\",\"id_parent\":\"32\"},{\"id\":\"40\",\"nom\":\"Mateur\",\"id_parent\":\"32\"},{\"id\":\"41\",\"nom\":\"MenzelBourguiba\",\"id_parent\":\"32\"},{\"id\":\"42\",\"nom\":\"MenzelJemil\",\"id_parent\":\"32\"},{\"id\":\"43\",\"nom\":\"RasJebel\",\"id_parent\":\"32\"},{\"id\":\"44\",\"nom\":\"Sejnane\",\"id_parent\":\"32\"},{\"id\":\"45\",\"nom\":\"Tinja\",\"id_parent\":\"32\"},{\"id\":\"46\",\"nom\":\"Utique\",\"id_parent\":\"32\"},{\"id\":\"47\",\"nom\":\"Gabes\",\"id_parent\":\"0\"},{\"id\":\"48\",\"nom\":\"ElHamma\",\"id_parent\":\"47\"},{\"id\":\"49\",\"nom\":\"ElMetouia\",\"id_parent\":\"47\"},{\"id\":\"50\",\"nom\":\"GabesMedina\",\"id_parent\":\"47\"},{\"id\":\"51\",\"nom\":\"GabesOuest\",\"id_parent\":\"47\"},{\"id\":\"52\",\"nom\":\"GabesSud\",\"id_parent\":\"47\"},{\"id\":\"53\",\"nom\":\"Ghannouche\",\"id_parent\":\"47\"},{\"id\":\"54\",\"nom\":\"Mareth\",\"id_parent\":\"47\"},{\"id\":\"55\",\"nom\":\"Matmata\",\"id_parent\":\"47\"},{\"id\":\"56\",\"nom\":\"MenzelHabib\",\"id_parent\":\"47\"},{\"id\":\"57\",\"nom\":\"NouvelleMatmata\",\"id_parent\":\"47\"},{\"id\":\"58\",\"nom\":\"Gafsa\",\"id_parent\":\"0\"},{\"id\":\"59\",\"nom\":\"Belkhir\",\"id_parent\":\"58\"},{\"id\":\"60\",\"nom\":\"ElGuettar\",\"id_parent\":\"58\"},{\"id\":\"61\",\"nom\":\"ElKsar\",\"id_parent\":\"58\"},{\"id\":\"62\",\"nom\":\"ElMdhilla\",\"id_parent\":\"58\"},{\"id\":\"63\",\"nom\":\"GafsaNord\",\"id_parent\":\"58\"},{\"id\":\"64\",\"nom\":\"GafsaSud\",\"id_parent\":\"58\"},{\"id\":\"65\",\"nom\":\"Metlaoui\",\"id_parent\":\"58\"},{\"id\":\"66\",\"nom\":\"Moulares\",\"id_parent\":\"58\"},{\"id\":\"67\",\"nom\":\"Redeyef\",\"id_parent\":\"58\"},{\"id\":\"68\",\"nom\":\"SidiAich\",\"id_parent\":\"58\"},{\"id\":\"69\",\"nom\":\"Sned\",\"id_parent\":\"58\"},{\"id\":\"70\",\"nom\":\"Jendouba\",\"id_parent\":\"0\"},{\"id\":\"71\",\"nom\":\"AinDraham\",\"id_parent\":\"70\"},{\"id\":\"72\",\"nom\":\"BaltaBouAouene\",\"id_parent\":\"70\"},{\"id\":\"73\",\"nom\":\"BouSalem\",\"id_parent\":\"70\"},{\"id\":\"74\",\"nom\":\"Fernana\",\"id_parent\":\"70\"},{\"id\":\"75\",\"nom\":\"Ghardimaou\",\"id_parent\":\"70\"},{\"id\":\"76\",\"nom\":\"Jendouba\",\"id_parent\":\"70\"},{\"id\":\"77\",\"nom\":\"JendoubaNord\",\"id_parent\":\"70\"},{\"id\":\"78\",\"nom\":\"OuedMliz\",\"id_parent\":\"70\"},{\"id\":\"79\",\"nom\":\"Tabarka\",\"id_parent\":\"70\"},{\"id\":\"80\",\"nom\":\"Kairouan\",\"id_parent\":\"0\"},{\"id\":\"81\",\"nom\":\"BouHajla\",\"id_parent\":\"80\"},{\"id\":\"82\",\"nom\":\"Chebika\",\"id_parent\":\"80\"},{\"id\":\"83\",\"nom\":\"Cherarda\",\"id_parent\":\"80\"},{\"id\":\"84\",\"nom\":\"ElAla\",\"id_parent\":\"80\"},{\"id\":\"85\",\"nom\":\"Haffouz\",\"id_parent\":\"80\"},{\"id\":\"86\",\"nom\":\"HajebElAyoun\",\"id_parent\":\"80\"},{\"id\":\"87\",\"nom\":\"KairouanNord\",\"id_parent\":\"80\"},{\"id\":\"88\",\"nom\":\"KairouanSud\",\"id_parent\":\"80\"},{\"id\":\"89\",\"nom\":\"Nasrallah\",\"id_parent\":\"80\"},{\"id\":\"90\",\"nom\":\"Oueslatia\",\"id_parent\":\"80\"},{\"id\":\"91\",\"nom\":\"Sbikha\",\"id_parent\":\"80\"},{\"id\":\"92\",\"nom\":\"Kasserine\",\"id_parent\":\"0\"},{\"id\":\"93\",\"nom\":\"ElAyoun\",\"id_parent\":\"92\"},{\"id\":\"94\",\"nom\":\"Ezzouhour\",\"id_parent\":\"92\"},{\"id\":\"95\",\"nom\":\"Feriana\",\"id_parent\":\"92\"},{\"id\":\"96\",\"nom\":\"Foussana\",\"id_parent\":\"92\"},{\"id\":\"97\",\"nom\":\"Haidra\",\"id_parent\":\"92\"},{\"id\":\"98\",\"nom\":\"HassiElFrid\",\"id_parent\":\"92\"},{\"id\":\"99\",\"nom\":\"Jediliane\",\"id_parent\":\"92\"},{\"id\":\"100\",\"nom\":\"KasserineNord\",\"id_parent\":\"92\"},{\"id\":\"101\",\"nom\":\"KasserineSud\",\"id_parent\":\"92\"},{\"id\":\"102\",\"nom\":\"MejelBelAbbes\",\"id_parent\":\"92\"},{\"id\":\"103\",\"nom\":\"Sbeitla\",\"id_parent\":\"92\"},{\"id\":\"104\",\"nom\":\"Sbiba\",\"id_parent\":\"92\"},{\"id\":\"105\",\"nom\":\"Thala\",\"id_parent\":\"92\"},{\"id\":\"106\",\"nom\":\"Kebili\",\"id_parent\":\"0\"},{\"id\":\"107\",\"nom\":\"Douz\",\"id_parent\":\"106\"},{\"id\":\"108\",\"nom\":\"ElFaouar\",\"id_parent\":\"106\"},{\"id\":\"109\",\"nom\":\"KebiliNord\",\"id_parent\":\"106\"},{\"id\":\"110\",\"nom\":\"KebiliSud\",\"id_parent\":\"106\"},{\"id\":\"111\",\"nom\":\"SoukElAhad\",\"id_parent\":\"106\"},{\"id\":\"112\",\"nom\":\"LaManouba\",\"id_parent\":\"0\"},{\"id\":\"113\",\"nom\":\"BorjElAmri\",\"id_parent\":\"112\"},{\"id\":\"114\",\"nom\":\"DouarHicher\",\"id_parent\":\"112\"},{\"id\":\"115\",\"nom\":\"ElBattan\",\"id_parent\":\"112\"},{\"id\":\"116\",\"nom\":\"Jedaida\",\"id_parent\":\"112\"},{\"id\":\"117\",\"nom\":\"Mannouba\",\"id_parent\":\"112\"},{\"id\":\"118\",\"nom\":\"Mornaguia\",\"id_parent\":\"112\"},{\"id\":\"119\",\"nom\":\"OuedEllil\",\"id_parent\":\"112\"},{\"id\":\"120\",\"nom\":\"Tebourba\",\"id_parent\":\"112\"},{\"id\":\"121\",\"nom\":\"LeKef\",\"id_parent\":\"0\"},{\"id\":\"122\",\"nom\":\"Dahmani\",\"id_parent\":\"121\"},{\"id\":\"123\",\"nom\":\"ElKsour\",\"id_parent\":\"121\"},{\"id\":\"124\",\"nom\":\"Jerissa\",\"id_parent\":\"121\"},{\"id\":\"125\",\"nom\":\"KalaaElKhasba\",\"id_parent\":\"121\"},{\"id\":\"126\",\"nom\":\"KalaatSinane\",\"id_parent\":\"121\"},{\"id\":\"127\",\"nom\":\"LeKefEst\",\"id_parent\":\"121\"},{\"id\":\"128\",\"nom\":\"LeKefOuest\",\"id_parent\":\"121\"},{\"id\":\"129\",\"nom\":\"LeSers\",\"id_parent\":\"121\"},{\"id\":\"130\",\"nom\":\"Nebeur\",\"id_parent\":\"121\"},{\"id\":\"131\",\"nom\":\"SakietSidiYoussef\",\"id_parent\":\"121\"},{\"id\":\"132\",\"nom\":\"Tajerouine\",\"id_parent\":\"121\"},{\"id\":\"133\",\"nom\":\"Touiref\",\"id_parent\":\"121\"},{\"id\":\"134\",\"nom\":\"Mahdia\",\"id_parent\":\"0\"},{\"id\":\"135\",\"nom\":\"BouMerdes\",\"id_parent\":\"134\"},{\"id\":\"136\",\"nom\":\"Chorbane\",\"id_parent\":\"134\"},{\"id\":\"137\",\"nom\":\"ElJem\",\"id_parent\":\"134\"},{\"id\":\"138\",\"nom\":\"Hbira\",\"id_parent\":\"134\"},{\"id\":\"139\",\"nom\":\"KsourEssaf\",\"id_parent\":\"134\"},{\"id\":\"140\",\"nom\":\"LaChebba\",\"id_parent\":\"134\"},{\"id\":\"141\",\"nom\":\"Mahdia\",\"id_parent\":\"134\"},{\"id\":\"142\",\"nom\":\"Melloulech\",\"id_parent\":\"134\"},{\"id\":\"143\",\"nom\":\"OuledChamakh\",\"id_parent\":\"134\"},{\"id\":\"144\",\"nom\":\"SidiAlouene\",\"id_parent\":\"134\"},{\"id\":\"145\",\"nom\":\"Souassi\",\"id_parent\":\"134\"},{\"id\":\"146\",\"nom\":\"M\\u00e9denine\",\"id_parent\":\"0\"},{\"id\":\"147\",\"nom\":\"Ajim\",\"id_parent\":\"146\"},{\"id\":\"148\",\"nom\":\"BenGuerdane\",\"id_parent\":\"146\"},{\"id\":\"149\",\"nom\":\"BeniKhedache\",\"id_parent\":\"146\"},{\"id\":\"150\",\"nom\":\"HoumetEssouk\",\"id_parent\":\"146\"},{\"id\":\"151\",\"nom\":\"MedenineNord\",\"id_parent\":\"146\"},{\"id\":\"152\",\"nom\":\"MedenineSud\",\"id_parent\":\"146\"},{\"id\":\"153\",\"nom\":\"Midoun\",\"id_parent\":\"146\"},{\"id\":\"154\",\"nom\":\"SidiMakhlouf\",\"id_parent\":\"146\"},{\"id\":\"155\",\"nom\":\"Zarzis\",\"id_parent\":\"146\"},{\"id\":\"156\",\"nom\":\"Monastir\",\"id_parent\":\"0\"},{\"id\":\"157\",\"nom\":\"Bekalta\",\"id_parent\":\"156\"},{\"id\":\"158\",\"nom\":\"Bembla\",\"id_parent\":\"156\"},{\"id\":\"159\",\"nom\":\"BeniHassen\",\"id_parent\":\"156\"},{\"id\":\"160\",\"nom\":\"Jemmal\",\"id_parent\":\"156\"},{\"id\":\"161\",\"nom\":\"KsarHelal\",\"id_parent\":\"156\"},{\"id\":\"162\",\"nom\":\"KsibetElMediouni\",\"id_parent\":\"156\"},{\"id\":\"163\",\"nom\":\"Moknine\",\"id_parent\":\"156\"},{\"id\":\"164\",\"nom\":\"Monastir\",\"id_parent\":\"156\"},{\"id\":\"165\",\"nom\":\"Ouerdanine\",\"id_parent\":\"156\"},{\"id\":\"166\",\"nom\":\"Sahline\",\"id_parent\":\"156\"},{\"id\":\"167\",\"nom\":\"SayadaLamtaBouHajar\",\"id_parent\":\"156\"},{\"id\":\"168\",\"nom\":\"Teboulba\",\"id_parent\":\"156\"},{\"id\":\"169\",\"nom\":\"Zeramdine\",\"id_parent\":\"156\"},{\"id\":\"170\",\"nom\":\"Nabeul\",\"id_parent\":\"0\"},{\"id\":\"171\",\"nom\":\"BeniKhalled\",\"id_parent\":\"170\"},{\"id\":\"172\",\"nom\":\"BeniKhiar\",\"id_parent\":\"170\"},{\"id\":\"173\",\"nom\":\"BouArgoub\",\"id_parent\":\"170\"},{\"id\":\"174\",\"nom\":\"DarChaabaneElfehri\",\"id_parent\":\"170\"},{\"id\":\"175\",\"nom\":\"ElHaouaria\",\"id_parent\":\"170\"},{\"id\":\"176\",\"nom\":\"ElMida\",\"id_parent\":\"170\"},{\"id\":\"177\",\"nom\":\"Grombalia\",\"id_parent\":\"170\"},{\"id\":\"178\",\"nom\":\"HammamElGhezaz\",\"id_parent\":\"170\"},{\"id\":\"179\",\"nom\":\"Hammamet\",\"id_parent\":\"170\"},{\"id\":\"180\",\"nom\":\"Kelibia\",\"id_parent\":\"170\"},{\"id\":\"181\",\"nom\":\"Korba\",\"id_parent\":\"170\"},{\"id\":\"182\",\"nom\":\"MenzelBouzelfa\",\"id_parent\":\"170\"},{\"id\":\"183\",\"nom\":\"MenzelTemime\",\"id_parent\":\"170\"},{\"id\":\"184\",\"nom\":\"Nabeul\",\"id_parent\":\"170\"},{\"id\":\"185\",\"nom\":\"Soliman\",\"id_parent\":\"170\"},{\"id\":\"186\",\"nom\":\"Takelsa\",\"id_parent\":\"170\"},{\"id\":\"187\",\"nom\":\"Sfax\",\"id_parent\":\"0\"},{\"id\":\"188\",\"nom\":\"Agareb\",\"id_parent\":\"187\"},{\"id\":\"189\",\"nom\":\"BirAliBenKhelifa\",\"id_parent\":\"187\"},{\"id\":\"190\",\"nom\":\"ElAmra\",\"id_parent\":\"187\"},{\"id\":\"191\",\"nom\":\"ElHencha\",\"id_parent\":\"187\"},{\"id\":\"192\",\"nom\":\"Esskhira\",\"id_parent\":\"187\"},{\"id\":\"193\",\"nom\":\"Ghraiba\",\"id_parent\":\"187\"},{\"id\":\"194\",\"nom\":\"Jebeniana\",\"id_parent\":\"187\"},{\"id\":\"195\",\"nom\":\"Kerkenah\",\"id_parent\":\"187\"},{\"id\":\"196\",\"nom\":\"Mahres\",\"id_parent\":\"187\"},{\"id\":\"197\",\"nom\":\"MenzelChaker\",\"id_parent\":\"187\"},{\"id\":\"198\",\"nom\":\"SakietEddaier\",\"id_parent\":\"187\"},{\"id\":\"199\",\"nom\":\"SakietEzzit\",\"id_parent\":\"187\"},{\"id\":\"200\",\"nom\":\"SfaxEst\",\"id_parent\":\"187\"},{\"id\":\"201\",\"nom\":\"SfaxSud\",\"id_parent\":\"187\"},{\"id\":\"202\",\"nom\":\"SfaxVille\",\"id_parent\":\"187\"},{\"id\":\"203\",\"nom\":\"SidiBouzid\",\"id_parent\":\"0\"},{\"id\":\"204\",\"nom\":\"BenOun\",\"id_parent\":\"203\"},{\"id\":\"205\",\"nom\":\"BirElHaffey\",\"id_parent\":\"203\"},{\"id\":\"206\",\"nom\":\"Cebbala\",\"id_parent\":\"203\"},{\"id\":\"207\",\"nom\":\"Jilma\",\"id_parent\":\"203\"},{\"id\":\"208\",\"nom\":\"Maknassy\",\"id_parent\":\"203\"},{\"id\":\"209\",\"nom\":\"MenzelBouzaiene\",\"id_parent\":\"203\"},{\"id\":\"210\",\"nom\":\"Mezzouna\",\"id_parent\":\"203\"},{\"id\":\"211\",\"nom\":\"OuledHaffouz\",\"id_parent\":\"203\"},{\"id\":\"212\",\"nom\":\"Regueb\",\"id_parent\":\"203\"},{\"id\":\"213\",\"nom\":\"SidiBouzidEst\",\"id_parent\":\"203\"},{\"id\":\"214\",\"nom\":\"SidiBouzidOuest\",\"id_parent\":\"203\"},{\"id\":\"215\",\"nom\":\"SoukJedid\",\"id_parent\":\"203\"},{\"id\":\"216\",\"nom\":\"Siliana\",\"id_parent\":\"0\"},{\"id\":\"217\",\"nom\":\"Bargou\",\"id_parent\":\"216\"},{\"id\":\"218\",\"nom\":\"BouArada\",\"id_parent\":\"216\"},{\"id\":\"219\",\"nom\":\"ElAroussa\",\"id_parent\":\"216\"},{\"id\":\"220\",\"nom\":\"Gaafour\",\"id_parent\":\"216\"},{\"id\":\"221\",\"nom\":\"Kesra\",\"id_parent\":\"216\"},{\"id\":\"222\",\"nom\":\"LeKrib\",\"id_parent\":\"216\"},{\"id\":\"223\",\"nom\":\"Makthar\",\"id_parent\":\"216\"},{\"id\":\"224\",\"nom\":\"Rohia\",\"id_parent\":\"216\"},{\"id\":\"225\",\"nom\":\"SidiBouRouis\",\"id_parent\":\"216\"},{\"id\":\"226\",\"nom\":\"SilianaNord\",\"id_parent\":\"216\"},{\"id\":\"227\",\"nom\":\"SilianaSud\",\"id_parent\":\"216\"},{\"id\":\"228\",\"nom\":\"Sousse\",\"id_parent\":\"0\"},{\"id\":\"229\",\"nom\":\"Akouda\",\"id_parent\":\"228\"},{\"id\":\"230\",\"nom\":\"BouFicha\",\"id_parent\":\"228\"},{\"id\":\"231\",\"nom\":\"Enfidha\",\"id_parent\":\"228\"},{\"id\":\"232\",\"nom\":\"HammamSousse\",\"id_parent\":\"228\"},{\"id\":\"233\",\"nom\":\"Hergla\",\"id_parent\":\"228\"},{\"id\":\"234\",\"nom\":\"KalaaElKebira\",\"id_parent\":\"228\"},{\"id\":\"235\",\"nom\":\"KalaaEssghira\",\"id_parent\":\"228\"},{\"id\":\"236\",\"nom\":\"Kondar\",\"id_parent\":\"228\"},{\"id\":\"237\",\"nom\":\"Msaken\",\"id_parent\":\"228\"},{\"id\":\"238\",\"nom\":\"SidiBouAli\",\"id_parent\":\"228\"},{\"id\":\"239\",\"nom\":\"SidiElHeni\",\"id_parent\":\"228\"},{\"id\":\"240\",\"nom\":\"SousseJaouhara\",\"id_parent\":\"228\"},{\"id\":\"241\",\"nom\":\"SousseRiadh\",\"id_parent\":\"228\"},{\"id\":\"242\",\"nom\":\"SousseVille\",\"id_parent\":\"228\"},{\"id\":\"243\",\"nom\":\"Tataouine\",\"id_parent\":\"0\"},{\"id\":\"244\",\"nom\":\"BirLahmar\",\"id_parent\":\"243\"},{\"id\":\"245\",\"nom\":\"Dhehiba\",\"id_parent\":\"243\"},{\"id\":\"246\",\"nom\":\"Ghomrassen\",\"id_parent\":\"243\"},{\"id\":\"247\",\"nom\":\"Remada\",\"id_parent\":\"243\"},{\"id\":\"248\",\"nom\":\"Smar\",\"id_parent\":\"243\"},{\"id\":\"249\",\"nom\":\"TataouineNord\",\"id_parent\":\"243\"},{\"id\":\"250\",\"nom\":\"TataouineSud\",\"id_parent\":\"243\"},{\"id\":\"251\",\"nom\":\"Tozeur\",\"id_parent\":\"0\"},{\"id\":\"252\",\"nom\":\"Degueche\",\"id_parent\":\"251\"},{\"id\":\"253\",\"nom\":\"Hezoua\",\"id_parent\":\"251\"},{\"id\":\"254\",\"nom\":\"Nefta\",\"id_parent\":\"251\"},{\"id\":\"255\",\"nom\":\"Tameghza\",\"id_parent\":\"251\"},{\"id\":\"256\",\"nom\":\"Tozeur\",\"id_parent\":\"251\"},{\"id\":\"257\",\"nom\":\"Tunis\",\"id_parent\":\"0\"},{\"id\":\"258\",\"nom\":\"BabBhar\",\"id_parent\":\"257\"},{\"id\":\"259\",\"nom\":\"BabSouika\",\"id_parent\":\"257\"},{\"id\":\"260\",\"nom\":\"Carthage\",\"id_parent\":\"257\"},{\"id\":\"261\",\"nom\":\"CiteElKhadra\",\"id_parent\":\"257\"},{\"id\":\"262\",\"nom\":\"ElHrairia\",\"id_parent\":\"257\"},{\"id\":\"263\",\"nom\":\"ElKabbaria\",\"id_parent\":\"257\"},{\"id\":\"264\",\"nom\":\"ElKram\",\"id_parent\":\"257\"},{\"id\":\"265\",\"nom\":\"ElMenzah\",\"id_parent\":\"257\"},{\"id\":\"266\",\"nom\":\"ElOmrane\",\"id_parent\":\"257\"},{\"id\":\"267\",\"nom\":\"ElOmraneSuperieur\",\"id_parent\":\"257\"},{\"id\":\"268\",\"nom\":\"ElOuerdia\",\"id_parent\":\"257\"},{\"id\":\"269\",\"nom\":\"Essijoumi\",\"id_parent\":\"257\"},{\"id\":\"270\",\"nom\":\"Ettahrir\",\"id_parent\":\"257\"},{\"id\":\"271\",\"nom\":\"Ezzouhour\",\"id_parent\":\"257\"},{\"id\":\"272\",\"nom\":\"JebelJelloud\",\"id_parent\":\"257\"},{\"id\":\"273\",\"nom\":\"LaGoulette\",\"id_parent\":\"257\"},{\"id\":\"274\",\"nom\":\"LaMarsa\",\"id_parent\":\"257\"},{\"id\":\"275\",\"nom\":\"LaMedina\",\"id_parent\":\"257\"},{\"id\":\"276\",\"nom\":\"LeBardo\",\"id_parent\":\"257\"},{\"id\":\"277\",\"nom\":\"SidiElBechir\",\"id_parent\":\"257\"},{\"id\":\"278\",\"nom\":\"SidiHassine\",\"id_parent\":\"257\"},{\"id\":\"279\",\"nom\":\"Zaghouan\",\"id_parent\":\"0\"},{\"id\":\"280\",\"nom\":\"BirMcherga\",\"id_parent\":\"279\"},{\"id\":\"281\",\"nom\":\"ElFahs\",\"id_parent\":\"279\"},{\"id\":\"282\",\"nom\":\"Ennadhour\",\"id_parent\":\"279\"},{\"id\":\"283\",\"nom\":\"HammamZriba\",\"id_parent\":\"279\"},{\"id\":\"284\",\"nom\":\"Saouef\",\"id_parent\":\"279\"},{\"id\":\"285\",\"nom\":\"Zaghouan\",\"id_parent\":\"279\"},{\"id\":\"286\",\"nom\":\"JardinsElMenzah1\",\"id_parent\":\"1\"},{\"id\":\"287\",\"nom\":\"JardinsElMenzah2\",\"id_parent\":\"1\"}]" ;

  static Ville get_ville_with_id(String? id){
    var emptyville = Ville();
    Iterable l = json.decode(ville_list_json);
    List<Ville> listville = List<Ville>.from(l.map((model) => Ville.fromJson(model)));
    Ville villefound = listville.firstWhere((Ville) => Ville.id == id, orElse: () => emptyville);
    return villefound;
  }

  static List<Ville> get_all_ville_parent(){
    Iterable l = json.decode(ville_list_json);
    List<Ville> listville = List<Ville>.from(l.map((model) => Ville.fromJson(model)));
    List<Ville> listvillefound = listville.where((Ville) => Ville.idParent == "0").toList();
    return listvillefound;
  }

  static List<Ville> get_all_ville_child(String id_parent){
    List<Ville> listvillechildfound = [] ;
    Iterable l = json.decode(ville_list_json);
    List<Ville> listville = List<Ville>.from(l.map((model) => Ville.fromJson(model)));
    listvillechildfound = listville.where((Ville) => Ville.idParent == id_parent).toList();

    Ville villeparent = new Ville();
    villeparent.id = id_parent ;
    villeparent.nom = "Tous" ;
    villeparent.id = id_parent ;
    listvillechildfound.insert(0,villeparent);

    return listvillechildfound;
  }




}
