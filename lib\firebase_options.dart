// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBhYEvRsExtcAfCN84MRKaDbcw7rkDglEw',
    appId: '1:606571785785:web:a6700f5d055f1801e9a7e8',
    messagingSenderId: '606571785785',
    projectId: 'tunisieenchere-9e92c',
    authDomain: 'tunisieenchere-9e92c.firebaseapp.com',
    databaseURL: 'https://tunisieenchere-9e92c-default-rtdb.firebaseio.com',
    storageBucket: 'tunisieenchere-9e92c.appspot.com',
    measurementId: 'G-QS5KLRD2DH',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDknRhtI5-sPM9sPDFosLeMEA9c5Se-jPs',
    appId: '1:606571785785:android:32a8d4c257fff2b1e9a7e8',
    messagingSenderId: '606571785785',
    projectId: 'tunisieenchere-9e92c',
    databaseURL: 'https://tunisieenchere-9e92c-default-rtdb.firebaseio.com',
    storageBucket: 'tunisieenchere-9e92c.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDhxbINe1Z94ghe2RcM7xcLT8mcAdPbNYM',
    appId: '1:606571785785:ios:95e51ff2c4f55e1be9a7e8',
    messagingSenderId: '606571785785',
    projectId: 'tunisieenchere-9e92c',
    databaseURL: 'https://tunisieenchere-9e92c-default-rtdb.firebaseio.com',
    storageBucket: 'tunisieenchere-9e92c.appspot.com',
    androidClientId: '606571785785-t7meiloq62rauohmen844rfft01h14p8.apps.googleusercontent.com',
    iosBundleId: 'com.mcdev.tunisieenchere',
  );

}