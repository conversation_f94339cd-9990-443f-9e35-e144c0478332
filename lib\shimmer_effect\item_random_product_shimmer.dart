import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ItemRandomProductShimmer extends StatefulWidget {

  const ItemRandomProductShimmer({super.key});

  @override
  State<ItemRandomProductShimmer> createState() => _ItemRandomProductShimmerState();
}

class _ItemRandomProductShimmerState extends State<ItemRandomProductShimmer> {
  @override
  Widget build(BuildContext context) {
    double item_size = MediaQuery.of(context).size.width;
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(
        width: item_size,
        height: 200,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(topLeft: Radius.circular(7), topRight: Radius.circular(7),bottomLeft: Radius.circular(7), bottomRight: Radius.circular(7)),
        ),
        margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
      ),
    );
  }
}


