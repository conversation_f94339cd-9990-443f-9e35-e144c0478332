class MessagerieVendeurItem {
  String? idDiscussion;
  String? idProduit;
  String? idAcheteur;
  String? nomAcheteur;
  String? idVendeur;
  String? nomVendeur;
  String? nomUtil;
  String? dateDiscussion;
  String? titreProduit;

  MessagerieVendeurItem(
      {this.idDiscussion,
        this.idProduit,
        this.idAcheteur,
        this.nomAcheteur,
        this.idVendeur,
        this.nomVendeur,
        this.nomUtil,
        this.dateDiscussion,
        this.titreProduit});

  MessagerieVendeurItem.fromJson(Map<String, dynamic> json) {
    idDiscussion = json['id_discussion'];
    idProduit = json['id_produit'];
    idAcheteur = json['id_acheteur'];
    nomAcheteur = json['nom_acheteur'];
    idVendeur = json['id_vendeur'];
    nomVendeur = json['nom_vendeur'];
    nomUtil = json['nom_util'];
    dateDiscussion = json['date_discussion'];
    titreProduit = json['titre_produit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id_discussion'] = this.idDiscussion;
    data['id_produit'] = this.idProduit;
    data['id_acheteur'] = this.idAcheteur;
    data['nom_acheteur'] = this.nomAcheteur;
    data['id_vendeur'] = this.idVendeur;
    data['nom_vendeur'] = this.nomVendeur;
    data['nom_util'] = this.nomUtil;
    data['date_discussion'] = this.dateDiscussion;
    data['titre_produit'] = this.titreProduit;
    return data;
  }
}
