class ResponseEnchere {
  String? etat;
  String? message;

  ResponseEnchere({this.etat, this.message});

  ResponseEnchere.fromJson(Map<String, dynamic> json) {
    etat = json['etat'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['etat'] = this.etat;
    data['message'] = this.message;
    return data;
  }
}
