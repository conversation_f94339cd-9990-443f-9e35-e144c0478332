import 'dart:io';

import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:my_application/shimmer_effect/item_store_shimmer.dart';
import 'package:my_application/store_detail.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:my_application/utils/string_constant.dart';
import 'package:my_application/utils/wishlist_store_sharedprefrence.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'dart:convert';

import 'Model/store.dart';

class WishlistStoreList extends StatefulWidget {
  const WishlistStoreList({super.key});

  @override
  State<WishlistStoreList> createState() => _WishlistStoreListState();
}

class _WishlistStoreListState extends State<WishlistStoreList> {
  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
  late List<Store> ListStore;

  String text_search = '';
  bool more_item = true;
  bool loader_bottom = false;
  double padding_top_list = 10;
  var _controller = TextEditingController();
  List<String> wishlist_store = [];

  @override
  void initState() {
    super.initState();
    loader_bottom = false;
    ListStore = [];
    wishlist_store = WishlistStoreSharedprefrence().WishlistStoreSharedprefrence_data();
    getwishlistlistStore();
    padding_top_list = 10;
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: Scaffold(
        backgroundColor: ColorConstant.background_home_page,
        appBar: AppBar(
          surfaceTintColor: Colors.white,
          elevation: 3,
          shadowColor: Colors.black,
          backgroundColor: Colors.white,
          title: const Text('Boutiques favoris'),
        ),
        body: Container(
          child: NotificationListener<ScrollEndNotification>(
              onNotification: (scrollEnd) {
                final metrics = scrollEnd.metrics;
                if (metrics.atEdge) {
                  bool isTop = metrics.pixels == 0;
                  if (isTop) {
                  } else {
                    if (more_item == true) {
                      setState(() {
                        more_item = false;
                      });
                      // add more item
                      setState(() {
                        getwishlistlistStore();
                        loader_bottom = true;
                      });
                    }
                  }
                }
                return true;
              },
              child: Stack(
                children: [
                  ListStore.isEmpty
                      ? ListView.builder(
                          padding: EdgeInsets.only(bottom: 50, top: padding_top_list),
                          itemBuilder: (BuildContext context, int index) => const ItemStoreShimmer(),
                          itemCount: 10,
                        )
                      : ListView.builder(
                          padding: EdgeInsets.only(bottom: 50, top: padding_top_list),
                          itemBuilder: (BuildContext context, int index) => _itemliststore(ListStore.elementAt(index), context, index),
                          itemCount: ListStore.length,
                        ),
                  if (padding_top_list != 10)
                    Container(
                      color: ColorConstant.background_home_page,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          children: [
                            Expanded(
                              flex: 8,
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: TextField(
                                  autofocus: true,
                                  controller: _controller,
                                  decoration: InputDecoration(
                                    hintText: 'Recherche boutique',
                                    suffixIcon: IconButton(
                                      onPressed: _controller.clear,
                                      icon: Icon(Icons.clear),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Container(
                                decoration: BoxDecoration(
                                  color: ColorConstant.red_enchere,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                height: 40,
                                width: 40,
                                child: const Center(
                                    child: Icon(
                                  Icons.search,
                                  color: Colors.white,
                                )))
                          ],
                        ),
                      ),
                    ),
                  loader_bottom == true
                      ? Positioned(
                          bottom: 0,
                          child: Container(
                            color: Colors.white,
                            height: 50,
                            width: MediaQuery.of(context).size.width,
                            child: const Center(
                              child: Text(
                                'Chargement...',
                                style: TextStyle(fontSize: 18, fontFamily: 'Araboto'),
                              ),
                            ),
                          ))
                      : SizedBox(),
                ],
              )),
        ),
      ),
    );
  }

  Widget _itemliststore(Store snapshot, context, int position) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
            context, MaterialPageRoute(builder: (context) => StoreDetail(idStore: snapshot.idStore!)));
      },
      child: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(topLeft: Radius.circular(7), topRight: Radius.circular(7)),
          ),
          margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
          child: Card(
              margin: const EdgeInsets.all(0),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(6),
              ),
              child: Padding(
                padding: const EdgeInsets.all(5.0),
                child: Container(
                    height: 150,
                    child: Column(children: [
                      Row(
                        children: [
                          Expanded(
                            flex: 3,
                            child: Center(
                              child: Container(
                                height: 90,
                                width: 90,
                                margin: EdgeInsets.only(left: 5, top: 5, bottom: 5, right: 10),
                                decoration: BoxDecoration(
                                  borderRadius: const BorderRadius.all(Radius.circular(7)),
                                  image: DecorationImage(
                                    image: NetworkImage("${snapshot.imageProfilStore}"),
                                    fit: BoxFit.cover,
                                    alignment: Alignment.center,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          Expanded(
                              flex: 7,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Text(
                                    '${snapshot.nomUtilStore}',
                                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                                  ),
                                  Text(
                                    '${snapshot.adresseStore}',
                                    style: TextStyle(color: Colors.black54, fontSize: 11),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Row(
                                    children: [
                                      const Icon(Icons.notifications_active, size: 16),
                                      Text(' Suivis (${snapshot.nbrAbonneStore})',
                                          style: const TextStyle(color: Colors.black54, fontSize: 12, fontWeight: FontWeight.w300)),
                                    ],
                                  ),
                                  Row(
                                    children: [
                                      const Icon(Icons.add_shopping_cart_outlined, size: 16),
                                      Text(' Nombre d\'annonces (${snapshot.nbrArticleStore})',
                                          style: const TextStyle(color: Colors.black54, fontSize: 12, fontWeight: FontWeight.w300)),
                                    ],
                                  )
                                ],
                              )),
                        ],
                      ),
                      const Divider(
                        endIndent: 10,
                        indent: 10,
                        color: Colors.black,
                        thickness: 0.3,
                      ),
                      IntrinsicHeight(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            const SizedBox(width: 1),
                            Expanded(
                              child: GestureDetector(
                                child: Container(
                                    color: Colors.transparent,
                                    padding: EdgeInsets.all(1),
                                    child: Icon(Icons.call, color: ColorConstant.second_color, size: 26)),
                                onTap: () {
                                  launchUrlString("tel://${snapshot.telStore}");
                                },
                              ),
                            ),
                            const VerticalDivider(
                              endIndent: 1,
                              indent: 1,
                              color: Colors.black,
                              thickness: 0.5,
                            ),
                            Expanded(
                              child: GestureDetector(
                                child: Container(
                                    color: Colors.transparent,
                                    padding: EdgeInsets.all(1),
                                    child: Icon(Icons.location_pin, color: ColorConstant.second_color, size: 26)),
                                onTap: () {
                                  if(Platform.isAndroid){
                                    launchMapOnAndroid('${snapshot.gpsStore}');
                                  }
                                  if(Platform.isIOS){
                                    lauchMapOnIOS('${snapshot.gpsStore}');
                                  }
                                },
                              ),
                            ),
                            const VerticalDivider(
                              endIndent: 1,
                              indent: 1,
                              color: Colors.black,
                              thickness: 0.5,
                            ),
                            wishlist_store.contains("${snapshot.idStore}")
                                ? Expanded(
                                  child: GestureDetector(
                                      // connected and in wishlist store
                                      child: Container(
                                          color: Colors.transparent,
                                          padding: EdgeInsets.all(1),
                                          child: Icon(Icons.notifications_active, color: ColorConstant.second_color, size: 26)),
                                      onTap: () {
                                        setState(() {
                                          WishlistStoreSharedprefrence().remove_from_WishlistStoreSharedprefrence_data("${snapshot.idStore}");
                                          wishlist_store = WishlistStoreSharedprefrence().WishlistStoreSharedprefrence_data();
                                          ListStore = ListStore;
                                        });
                                      },
                                    ),
                                )
                                : Expanded(
                                  child: GestureDetector(
                                      // connected and not in wishlist store
                                      child: Container(
                                          color: Colors.transparent,
                                          padding: EdgeInsets.all(1),
                                          child: Icon(Icons.notifications, color: ColorConstant.gary_marker, size: 26)),
                                      onTap: () {
                                        setState(() {
                                          WishlistStoreSharedprefrence().add_to_WishlistStoreSharedprefrence_data("${snapshot.idStore}");
                                          wishlist_store = WishlistStoreSharedprefrence().WishlistStoreSharedprefrence_data();
                                          ListStore = ListStore;
                                        });
                                      },
                                    ),
                                ),
                            const SizedBox(width: 1),
                          ],
                        ),
                      )
                    ])),
              ))),
    );
  }

  Future<void> getwishlistlistStore() async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_store_wishlist.php?token=${token}'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      List<Store> liststore = List<Store>.from(l.map((model) => Store.fromJson(model)));

      setState(() {
        if (liststore.length != 10) {
          more_item = false;
        } else {
          more_item = true;
        }
      });
      setState(() {
        ListStore.addAll(liststore);
        loader_bottom = false;
      });
    } else {}
  }



  void launchMapOnAndroid(String uri_string) async {
    try {
      const String markerLabel = 'Here';
      final url = Uri.parse(uri_string);
      await launchUrl(url);
    } catch (error) {
    }
  }

  void lauchMapOnIOS(String uri_string) async {
    try {
      final url = Uri.parse(uri_string);
      if (await canLaunchUrl(url)) {
        await launchUrl(url);
      } else {
        throw 'Could not launch $url';
      }
    } catch (error) {
    }
  }


}
