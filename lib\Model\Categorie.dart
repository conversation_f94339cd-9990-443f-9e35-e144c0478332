class Categorie {
  String? id;
  String? nomCategorieProduit;
  String? icon;
  String? idTypeProduit;

  Categorie({this.id, this.nomCategorieProduit, this.icon, this.idTypeProduit});

  Categorie.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    nomCategorieProduit = json['nom_categorie_produit'];
    icon = json['icon'];
    idTypeProduit = json['id_type_produit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['nom_categorie_produit'] = this.nomCategorieProduit;
    data['icon'] = this.icon;
    data['id_type_produit'] = this.idTypeProduit;
    return data;
  }
}