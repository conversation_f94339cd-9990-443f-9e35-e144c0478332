class DataAttribute {
  String? id;
  String? nomAttributeValue;

  DataAttribute({this.id, this.nomAttributeValue});

  DataAttribute.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    nomAttributeValue = json['nom_attribute_value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['nom_attribute_value'] = this.nomAttributeValue;
    return data;
  }
}