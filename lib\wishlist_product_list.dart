import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:my_application/shimmer_effect/item_product_shimmer.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:my_application/utils/string_constant.dart';
import 'package:my_application/widget/itemrandomproduct.dart';
import 'dart:convert';

import 'Model/product.dart';

class WishlistProductList extends StatefulWidget {
  const WishlistProductList({super.key});

  @override
  State<WishlistProductList> createState() => _WishlistProductListState();
}

class _WishlistProductListState extends State<WishlistProductList> {
  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
  late List<Product> ListProduct;

  String text_search = '';
  bool more_item = true;
  bool loader_bottom = false;
  double padding_top_list = 10;
  bool emptyproduct = false ;

  @override
  void initState() {
    super.initState();
    loader_bottom = false;
    ListProduct = [];
    getlistProduct_wishlist();
    padding_top_list = 10;
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: Scaffold(
        backgroundColor: ColorConstant.background_home_page,
        appBar: AppBar(
          surfaceTintColor: Colors.white,
          elevation: 3,
          shadowColor: Colors.black,
          backgroundColor: Colors.white,
          title: const Text('Favoris'),
        ),
        body: SingleChildScrollView(
          child: NotificationListener<ScrollEndNotification>(
              onNotification: (scrollEnd) {
                final metrics = scrollEnd.metrics;
                if (metrics.atEdge) {
                  bool isTop = metrics.pixels == 0;
                  if (isTop) {
                  } else {
                    if (more_item == true) {
                      setState(() {
                        more_item = false;
                      });
                      // add more item
                      setState(() {
                        getlistProduct_wishlist();
                        loader_bottom = true;
                      });
                    }
                  }
                }
                return true;
              },
              child: Stack(
                children: [
                  ListProduct.isEmpty
                      ? emptyproduct == false ? GridView.builder(
                          gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                              mainAxisExtent: 250, maxCrossAxisExtent: 250, crossAxisSpacing: 0, mainAxisSpacing: 0),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: 12,
                          itemBuilder: (BuildContext ctx, index) {
                            return const ItemProductShimmer();
                          })
                        : const Center(child : Text("Aucun produit"))
                      : GridView.builder(
                          padding: const EdgeInsets.only(top: 20),
                          gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                              mainAxisExtent: 280, maxCrossAxisExtent: 250, crossAxisSpacing: 0, mainAxisSpacing: 0),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: ListProduct.length,
                          itemBuilder: (BuildContext ctx, index) {
                            return Itemrandomproduct(snapshot: ListProduct.elementAt(index), context: context, full: 2);
                          }),
                  loader_bottom == true
                      ? Positioned(
                          bottom: 0,
                          child: Container(
                            color: Colors.white,
                            height: 50,
                            width: MediaQuery.of(context).size.width,
                            child: const Center(
                              child: Text(
                                'Chargement...',
                                style: TextStyle(fontSize: 18, fontFamily: 'Araboto'),
                              ),
                            ),
                          ))
                      : SizedBox(),
                ],
              )),
        ),
      ),
    );
  }

  Future<void> getlistProduct_wishlist() async {
    String? token = await _prefs.then((data) => data.getString('user_token'));
    List<Product> newlistproduct = [];
    var requestUrl = '${StringConstant.base_url}api/get_wishlist.php.php?token=${token}';
    var res = await http.get(Uri.parse(requestUrl));

    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      newlistproduct = List<Product>.from(l.map((model) => Product.fromJson(model)));
      setState(() {
        if (newlistproduct.length != 12) {
          more_item = false;
        } else {
          more_item = true;
        }
      });
      setState(() {
        ListProduct.addAll(newlistproduct);
        loader_bottom = false;
      });
    } else {}


    if(ListProduct.length == 0){
      emptyproduct = true ;
    }
  }
}
