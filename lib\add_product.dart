import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:my_application/utils/string_constant.dart';
import 'package:my_application/widget/show_custom_loader_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';

class AddProduct extends StatefulWidget {
  String id_offre ;
  AddProduct({required this.id_offre ,super.key});

  @override
  State<AddProduct> createState() => _AddProductState();
}

class _AddProductState extends State<AddProduct> {
  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();

  late final WebViewController _controller;
  int progress_loading = 0 ;


  @override
  void initState() {
    super.initState();
    initwebview() ;
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: Scaffold(
          backgroundColor: ColorConstant.background_home_page,
          appBar: AppBar(
            surfaceTintColor: Colors.white,
            elevation: 3,
            shadowColor: Colors.black,
            backgroundColor: Colors.white,
            title: const Text('Ajouter une nouvelle offre'),
          ),
          body: progress_loading == 100
              ? WebViewWidget(controller: _controller)
              : Center(child: const CircularProgressIndicator())
      ),
    );
  }

  void addFileSelectionListener() async {
    if (Platform.isAndroid) {
      final androidController = _controller.platform as AndroidWebViewController;
      await androidController.setOnShowFileSelector(_androidFilePicker);
    }
  }

  Future<List<String>> _androidFilePicker(final FileSelectorParams params) async {
    final result = await FilePicker.platform.pickFiles();

    if (result != null && result.files.single.path != null) {
      final file = File(result.files.single.path!);
      return [file.uri.toString()];
    }
    return [];
  }


  initwebview() async {
    String? token = await _prefs.then((data) => data.getString('user_token'));

    String add_param_url = "" ;

    if(widget.id_offre != ""){
      add_param_url = "&id=${widget.id_offre}" ;
    }

    // #docregion platform_features
    late final PlatformWebViewControllerCreationParams params;
    params = const PlatformWebViewControllerCreationParams();

    final WebViewController controller = WebViewController.fromPlatformCreationParams(params,onPermissionRequest: (WebViewPermissionRequest request) {
      request.grant();
    },);
    // #enddocregion platform_features

    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(

          onProgress: (int progress) {
            setState(() {
              progress_loading  = progress ;
            });
            debugPrint('WebView is loading (progress : $progress%)');
          },
          onPageStarted: (String url) {
            debugPrint('Page started loading: $url');
          },
          onPageFinished: (String url) {
            debugPrint('Page finished loading: $url');
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('''
Page resource error:
  code: ${error.errorCode}
  description: ${error.description}
  errorType: ${error.errorType}
  isForMainFrame: ${error.isForMainFrame}
          ''');
          },
          onNavigationRequest: (NavigationRequest request) {
            debugPrint('allowing navigation to ${request.url}');
            return NavigationDecision.navigate;
          },
          onHttpError: (HttpResponseError error) {
            debugPrint('Error occurred on page: ${error.response?.statusCode}');
          },
          onUrlChange: (UrlChange change) {
            debugPrint('url change to ${change.url}');
          },
          onHttpAuthRequest: (HttpAuthRequest request) {
          },
        ),
      )
      ..addJavaScriptChannel(
        'Toaster',
        onMessageReceived: (JavaScriptMessage message) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(message.message)),
          );
        },
      )
      ..loadRequest(Uri.parse("${StringConstant.base_url}add_new_offre.php?token=${token}${add_param_url}"));

    setState(() {
      _controller = controller;
    });

    addFileSelectionListener();

  }

}
