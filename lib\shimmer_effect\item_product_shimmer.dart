import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class ItemProductShimmer extends StatefulWidget {

  const ItemProductShimmer({super.key});

  @override
  State<ItemProductShimmer> createState() => _ItemProductShimmerState();
}

class _ItemProductShimmerState extends State<ItemProductShimmer> {
  @override
  Widget build(BuildContext context) {
    double item_size = MediaQuery.of(context).size.width / 2 ;
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(
        width: item_size,
        height: 200,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(topLeft: Radius.circular(7), topRight: Radius.circular(7),bottomLeft: Radius.circular(7), bottomRight: Radius.circular(7)),
        ),
        margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
      ),
    );
  }
}


