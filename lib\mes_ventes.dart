import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:my_application/Model/mes_chiffres.dart';
import 'package:my_application/shimmer_effect/item_vente_shimmer.dart';
import 'package:my_application/utils/color_constant.dart';
import 'package:my_application/utils/string_constant.dart';
import 'package:my_application/widget/build_item_vente.dart';
import 'package:my_application/widget/show_custom_loader_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'Model/response_request.dart';
import 'Model/vente.dart';

class MesVentes extends StatefulWidget {
  MesVentes({super.key});

  @override
  State<MesVentes> createState() => _MesVentesState();
}

class _MesVentesState extends State<MesVentes> {
  final Future<SharedPreferences> _prefs = SharedPreferences.getInstance();

  List<Vente> _ventes = [];
  List<Vente> _brouillons = [];
  List<Vente> _nonvendu = [];
  List<Vente> _vendu = [];

  bool loader_shimmer_ventes = true ;
  bool loader_shimmer_brouillons = true ;
  bool loader_shimmer_nonvendu = true ;
  bool loader_shimmer_vendu = true ;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      minimum: const EdgeInsets.only(top: 16.0),
      child: DefaultTabController(
        length: 4,
        child: Scaffold(
          backgroundColor: ColorConstant.background_home_page,
          appBar: AppBar(
            surfaceTintColor: Colors.white,
            elevation: 3,
            shadowColor: Colors.black,
            backgroundColor: Colors.white,
            title: const Text('Mes ventes'),
            bottom: const TabBar(
              tabAlignment: TabAlignment.start,
              isScrollable: true,
              indicatorSize: TabBarIndicatorSize.tab,
              tabs: [
                Tab(text: "A VENDRE"),
                Tab(text: "BROUILLONS"),
                Tab(text: "TERMINÉS NON VENDU"),
                Tab(text: "VENDUS"),
              ],
            ),
            actions: [
              IconButton(
                  onPressed: () {

                    get_info_mes_chiffres();

                  },
                  icon: const Icon(
                    Icons.bar_chart,
                  )),
            ],
          ),
          body: Padding(
            padding: const EdgeInsets.all(8.0),
            child: TabBarView(
              children: [
                _buildListView(_ventes, 'avendre'),
                _buildListView(_brouillons, 'brouillons'),
                _buildListView(_nonvendu, 'nonvendu'),
                _buildListView(_vendu, 'vendu'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// get list mes venetes
  Future<List<Vente>> get_mes_ventes(String type) async {
    List<Vente> listventes = [];
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_vente.php?token=${token}&type=${type}'));
    //var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_vente.php?token=${token}&type=${type}'));
    if (res.statusCode == 200) {
      Iterable l = json.decode(res.body);
      listventes = List<Vente>.from(l.map((model) => Vente.fromJson(model)));
    }
    switch (type) {
      case 'avendre' :
        loader_shimmer_ventes = false ;
        break;
      case 'brouillons' :
        loader_shimmer_brouillons = false ;
        break;
      case 'nonvendu' :
        loader_shimmer_nonvendu = false ;
        break;
      case 'vendu' :
        loader_shimmer_vendu = false ;
        break;
    }
    return listventes;
  }

  Future<void> _loadData() async {
    _ventes = await get_mes_ventes('avendre');
    _brouillons = await get_mes_ventes('brouillons');
    _nonvendu = await get_mes_ventes('nonvendu');
    _vendu = await get_mes_ventes('vendu');
    setState(() {});
  }

  Widget _buildListView(List<Vente> items, String type) {

    bool isStillloading = true ;

    switch (type) {
      case 'avendre' :
        isStillloading = loader_shimmer_ventes  ;
        break;
      case 'brouillons' :
        isStillloading  = loader_shimmer_brouillons  ;
        break;
      case 'nonvendu' :
        isStillloading  = loader_shimmer_nonvendu ;
        break;
      case 'vendu' :
        isStillloading  = loader_shimmer_vendu ;
        break;
    }
    return items.isEmpty
        ? isStillloading
          ? ListView.builder(
              shrinkWrap: true,
              scrollDirection: Axis.vertical,
              itemCount: 8,
              itemBuilder: (context, int index) {
                return const ItemVenteShimmer();
              })
           : const Center(child: Text('Aucune information'))
        : ListView.builder(
            shrinkWrap: true,
            scrollDirection: Axis.vertical,
            itemCount: items.length,
            itemBuilder: (context, int index) {
              return BuildItemVente(
                snapshot: items[index],
                context: context,
                type: type,
                onRemove: () {
                  showDialogDeleteProduct(items,type, index);
                },
              );
            });
  }

  /// dialog delete product
  showDialogDeleteProduct(List<Vente> items, String type, int index) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: const Text(
                'Suppression',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.only(left: 0, right: 0, top: 0, bottom: 0),
                            margin: const EdgeInsets.all(0),
                            child: const Center(child: Text("Voulez-vous supprimer cette offre ?"),)
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Non'),
                ),
                TextButton(
                  onPressed: () {
                    // Implement logic delmete product
                    Navigator.of(context).pop();
                    remove_product(items,type,index,items[index].id!);
                  },
                  child: const Text('Oui'),
                ),
              ],
            );
          });
        });
  }


  /// remove product
  void remove_product(List<Vente> items, String type, int index , String id_supp) async {
    print(id_supp);
    ShowCustomLoaderDialog().showCustomDialog(context);
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/remove_offre.php?token=${token}&idsupp=${id_supp}'));
    if (res.statusCode == 200) {
      ResponseRequest _response_request = ResponseRequest.fromJson(json.decode(res.body));
      if (_response_request.etat == "1") {
        setState(() {
          items.removeAt(index);
        });
      }
    }
    Navigator.of(context).pop();
  }
  /// get information statistique vente
  void get_info_mes_chiffres() async {
    ShowCustomLoaderDialog().showCustomDialog(context);
    String? token = await _prefs.then((data) => data.getString('user_token'));
    var res = await http.get(Uri.parse('${StringConstant.base_url}api/get_mes_chiffres.php?token=${token}'));
    Navigator.of(context).pop();
    if (res.statusCode == 200) {
      MesChiffres _response_request = MesChiffres.fromJson(json.decode(res.body));
      showDialogMesChiffres(_response_request.nbrVisitePerStore!,_response_request.totalVisiteForAllProduct!);
    }
  }

  showDialogMesChiffres(String _nbr_visite_per_store , String _total_visite_for_all_product){
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              title: SizedBox(
                height: 50,
                child: Stack(

                  children: [
                    const Center(
                      heightFactor: 50,
                      child: Text(
                        'Mes chiffres',
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                    Positioned(
                      top:0,
                      right: 0,
                      child: IconButton(
                        padding: const EdgeInsets.all(0),
                          onPressed: (){
                            Navigator.of(context).pop();
                          },
                          icon: const Icon(
                            Icons.close,
                            color: Colors.black38,
                            size: 18,
                          )),
                    )
                  ],
                ),
              ),
              content: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                              alignment: Alignment.center,
                              padding: const EdgeInsets.only(left: 0, right: 0, top: 0, bottom: 0),
                              margin: const EdgeInsets.all(0),
                              child: Center(child: Column(
                                children: [
                                  Text("Nombre de visites par boutique"),
                                  SizedBox(height: 10),
                                  Text("${_nbr_visite_per_store}"),
                                  SizedBox(height: 10),
                                  Text("Total des visites sur tous les articles"),
                                  SizedBox(height: 10),
                                  Text("${_total_visite_for_all_product}"),
                                ],
                              ),)
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                  ],
                ),
              )
            );
          });
        });
  }
}
