class ResponseInscription {
  String? to;
  String? message;
  String? error;

  ResponseInscription({this.to, this.message, this.error});

  ResponseInscription.fromJson(Map<String, dynamic> json) {
    to = json['to'];
    message = json['message'];
    error = json['error'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['to'] = this.to;
    data['message'] = this.message;
    data['error'] = this.error;
    return data;
  }
}
